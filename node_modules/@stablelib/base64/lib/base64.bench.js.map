{"version": 3, "file": "base64.bench.js", "sourceRoot": "", "sources": ["../base64.bench.ts"], "names": [], "mappings": ";AAAA,sCAAsC;AACtC,6CAA6C;;AAE7C,mCAA0C;AAC1C,kDAAkE;AAElE,IAAI,GAAG,GAAG,mBAAO,CAAC,IAAI,CAAC,CAAC;AACxB,IAAM,MAAM,GAAG,eAAM,CAAC,GAAG,CAAC,CAAC;AAE3B,kBAAM,CAAC,eAAe,EAAE,qBAAS,CAAC,cAAM,OAAA,eAAM,CAAC,GAAG,CAAC,EAAX,CAAW,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;AAClE,6DAA6D;AAC7D,kBAAM,CAAC,eAAe,EAAE,qBAAS,CAAC,cAAM,OAAA,eAAM,CAAC,MAAM,CAAC,EAAd,CAAc,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;AAIrE,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;IAC/B,4CAA4C;IAC5C,IAAM,SAAO,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACjC,IAAM,YAAU,GAAG,SAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAE9C,kBAAM,CAAC,wBAAwB,EAAE,qBAAS,CAAC;QACvC,OAAA,SAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC;IAA1B,CAA0B,EAAE,SAAO,CAAC,MAAM,CAAC,CAAC,CAAC;IACjD,kBAAM,CAAC,wBAAwB,EAAE,qBAAS,CAAC;QACvC,OAAA,MAAM,CAAC,IAAI,CAAC,YAAU,EAAE,QAAQ,CAAC;IAAjC,CAAiC,EAAE,SAAO,CAAC,MAAM,CAAC,CAAC,CAAC;CAC3D"}