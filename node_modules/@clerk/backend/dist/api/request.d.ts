import type { Clerk<PERSON>IError } from '@clerk/types';
type ClerkBackendApiRequestOptionsUrlOrPath = {
    url: string;
    path?: string;
} | {
    url?: string;
    path: string;
};
type ClerkBackendApiRequestOptionsBodyParams = {
    bodyParams: Record<string, unknown> | Array<Record<string, unknown>>;
    options?: {
        /**
         * If true, snakecases the keys of the bodyParams object recursively.
         * @default false
         */
        deepSnakecaseBodyParamKeys?: boolean;
    };
} | {
    bodyParams?: never;
    options?: {
        deepSnakecaseBodyParamKeys?: never;
    };
};
export type ClerkBackendApiRequestOptions = {
    method: 'GET' | 'POST' | 'PATCH' | 'DELETE' | 'PUT';
    queryParams?: Record<string, unknown>;
    headerParams?: Record<string, string>;
    formData?: FormData;
} & ClerkBackendApiRequestOptionsUrlOrPath & ClerkBackendApiRequestOptionsBodyParams;
export type Clerk<PERSON><PERSON>endApiResponse<T> = {
    data: T;
    errors: null;
    totalCount?: number;
} | {
    data: null;
    errors: ClerkAPIError[];
    totalCount?: never;
    clerkTraceId?: string;
    status?: number;
    statusText?: string;
    retryAfter?: number;
};
export type RequestFunction = ReturnType<typeof buildRequest>;
type BuildRequestOptions = {
    secretKey?: string;
    apiUrl?: string;
    apiVersion?: string;
    userAgent?: string;
    /**
     * Allow requests without specifying a secret key. In most cases this should be set to `false`.
     * @default true
     */
    requireSecretKey?: boolean;
    /**
     * If true, omits the API version from the request URL path.
     * This is required for bapi-proxy endpoints, which do not use versioning in the URL.
     *
     * Note: API versioning for these endpoints is instead handled via the `Clerk-API-Version` HTTP header.
     *
     * @default false
     */
    skipApiVersionInUrl?: boolean;
};
export declare function buildRequest(options: BuildRequestOptions): LegacyRequestFunction;
type LegacyRequestFunction = <T>(requestOptions: ClerkBackendApiRequestOptions) => Promise<T>;
export {};
//# sourceMappingURL=request.d.ts.map