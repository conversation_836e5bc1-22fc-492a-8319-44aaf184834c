// src/constants.ts
var LEGACY_DEV_INSTANCE_SUFFIXES = [".lcl.dev", ".lclstage.dev", ".lclclerk.com"];
var CURRENT_DEV_INSTANCE_SUFFIXES = [".accounts.dev", ".accountsstage.dev", ".accounts.lclclerk.com"];
var DEV_OR_STAGING_SUFFIXES = [
  ".lcl.dev",
  ".stg.dev",
  ".lclstage.dev",
  ".stgstage.dev",
  ".dev.lclclerk.com",
  ".stg.lclclerk.com",
  ".accounts.lclclerk.com",
  "accountsstage.dev",
  "accounts.dev"
];
var LOCAL_ENV_SUFFIXES = [".lcl.dev", "lclstage.dev", ".lclclerk.com", ".accounts.lclclerk.com"];
var STAGING_ENV_SUFFIXES = [".accountsstage.dev"];
var LOCAL_API_URL = "https://api.lclclerk.com";
var STAGING_API_URL = "https://api.clerkstage.dev";
var PROD_API_URL = "https://api.clerk.com";
function iconImageUrl(id, format = "svg") {
  return `https://img.clerk.com/static/${id}.${format}`;
}

export {
  LEGACY_DEV_INSTANCE_SUFFIXES,
  CURRENT_DEV_INSTANCE_SUFFIXES,
  DEV_OR_STAGING_SUFFIXES,
  LOCAL_ENV_SUFFIXES,
  STAGING_ENV_SUFFIXES,
  LOCAL_API_URL,
  STAGING_API_URL,
  PROD_API_URL,
  iconImageUrl
};
//# sourceMappingURL=chunk-I6MTSTOF.mjs.map