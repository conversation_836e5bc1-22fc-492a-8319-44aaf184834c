{"version": 3, "sources": ["../../src/dom/index.ts", "../../src/dom/waitForElement.ts"], "sourcesContent": ["export { waitForElement } from './waitForElement';\n", "/**\n * Uses a MutationObserver to wait for an element to be added to the DOM.\n */\nexport function waitForElement(selector: string): Promise<HTMLElement | null> {\n  return new Promise(resolve => {\n    if (document.querySelector(selector)) {\n      return resolve(document.querySelector(selector) as HTMLElement);\n    }\n\n    const observer = new MutationObserver(() => {\n      if (document.querySelector(selector)) {\n        observer.disconnect();\n        resolve(document.querySelector(selector) as HTMLElement);\n      }\n    });\n\n    observer.observe(document.body, { childList: true, subtree: true });\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACGO,SAAS,eAAe,UAA+C;AAC5E,SAAO,IAAI,QAAQ,aAAW;AAC5B,QAAI,SAAS,cAAc,QAAQ,GAAG;AACpC,aAAO,QAAQ,SAAS,cAAc,QAAQ,CAAgB;AAAA,IAChE;AAEA,UAAM,WAAW,IAAI,iBAAiB,MAAM;AAC1C,UAAI,SAAS,cAAc,QAAQ,GAAG;AACpC,iBAAS,WAAW;AACpB,gBAAQ,SAAS,cAAc,QAAQ,CAAgB;AAAA,MACzD;AAAA,IACF,CAAC;AAED,aAAS,QAAQ,SAAS,MAAM,EAAE,WAAW,MAAM,SAAS,KAAK,CAAC;AAAA,EACpE,CAAC;AACH;", "names": []}