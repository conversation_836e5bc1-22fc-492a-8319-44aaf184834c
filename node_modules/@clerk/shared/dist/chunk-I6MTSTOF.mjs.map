{"version": 3, "sources": ["../src/constants.ts"], "sourcesContent": ["export const LEGACY_DEV_INSTANCE_SUFFIXES = ['.lcl.dev', '.lclstage.dev', '.lclclerk.com'];\nexport const CURRENT_DEV_INSTANCE_SUFFIXES = ['.accounts.dev', '.accountsstage.dev', '.accounts.lclclerk.com'];\nexport const DEV_OR_STAGING_SUFFIXES = [\n  '.lcl.dev',\n  '.stg.dev',\n  '.lclstage.dev',\n  '.stgstage.dev',\n  '.dev.lclclerk.com',\n  '.stg.lclclerk.com',\n  '.accounts.lclclerk.com',\n  'accountsstage.dev',\n  'accounts.dev',\n];\nexport const LOCAL_ENV_SUFFIXES = ['.lcl.dev', 'lclstage.dev', '.lclclerk.com', '.accounts.lclclerk.com'];\nexport const STAGING_ENV_SUFFIXES = ['.accountsstage.dev'];\nexport const LOCAL_API_URL = 'https://api.lclclerk.com';\nexport const STAGING_API_URL = 'https://api.clerkstage.dev';\nexport const PROD_API_URL = 'https://api.clerk.com';\n\n/**\n * Returns the URL for a static image\n * using the new img.clerk.com service\n */\nexport function iconImageUrl(id: string, format: 'svg' | 'jpeg' = 'svg'): string {\n  return `https://img.clerk.com/static/${id}.${format}`;\n}\n"], "mappings": ";AAAO,IAAM,+BAA+B,CAAC,YAAY,iBAAiB,eAAe;AAClF,IAAM,gCAAgC,CAAC,iBAAiB,sBAAsB,wBAAwB;AACtG,IAAM,0BAA0B;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACO,IAAM,qBAAqB,CAAC,YAAY,gBAAgB,iBAAiB,wBAAwB;AACjG,IAAM,uBAAuB,CAAC,oBAAoB;AAClD,IAAM,gBAAgB;AACtB,IAAM,kBAAkB;AACxB,IAAM,eAAe;AAMrB,SAAS,aAAa,IAAY,SAAyB,OAAe;AAC/E,SAAO,gCAAgC,EAAE,IAAI,MAAM;AACrD;", "names": []}