{"version": 3, "sources": ["../src/authorization-errors.ts"], "sourcesContent": ["import type { ReverificationConfig } from '@clerk/types';\n\ntype ClerkError<T> = {\n  clerk_error: T;\n};\n\nconst REVERIFICATION_REASON = 'reverification-error';\n\ntype ReverificationError<M extends { metadata?: any } = { metadata: unknown }> = ClerkError<\n  {\n    type: 'forbidden';\n    reason: typeof REVERIFICATION_REASON;\n  } & M\n>;\n\nconst reverificationError = <MC extends ReverificationConfig>(\n  missingConfig?: MC,\n): ReverificationError<{\n  metadata?: {\n    reverification?: MC;\n  };\n}> => ({\n  clerk_error: {\n    type: 'forbidden',\n    reason: REVERIFICATION_REASON,\n    metadata: {\n      reverification: missingConfig,\n    },\n  },\n});\n\nconst reverificationErrorResponse = (...args: Parameters<typeof reverificationError>) =>\n  new Response(JSON.stringify(reverificationError(...args)), {\n    status: 403,\n  });\n\nconst isReverificationHint = (result: any): result is ReturnType<typeof reverificationError> => {\n  return (\n    result &&\n    typeof result === 'object' &&\n    'clerk_error' in result &&\n    result.clerk_error?.type === 'forbidden' &&\n    result.clerk_error?.reason === REVERIFICATION_REASON\n  );\n};\n\nexport { reverificationError, reverificationErrorResponse, isReverificationHint };\n"], "mappings": ";AAMA,IAAM,wBAAwB;AAS9B,IAAM,sBAAsB,CAC1B,mBAKK;AAAA,EACL,aAAa;AAAA,IACX,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU;AAAA,MACR,gBAAgB;AAAA,IAClB;AAAA,EACF;AACF;AAEA,IAAM,8BAA8B,IAAI,SACtC,IAAI,SAAS,KAAK,UAAU,oBAAoB,GAAG,IAAI,CAAC,GAAG;AAAA,EACzD,QAAQ;AACV,CAAC;AAEH,IAAM,uBAAuB,CAAC,WAAkE;AAC9F,SACE,UACA,OAAO,WAAW,YAClB,iBAAiB,UACjB,OAAO,aAAa,SAAS,eAC7B,OAAO,aAAa,WAAW;AAEnC;", "names": []}