"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/constants.ts
var constants_exports = {};
__export(constants_exports, {
  CURRENT_DEV_INSTANCE_SUFFIXES: () => CURRENT_DEV_INSTANCE_SUFFIXES,
  DEV_OR_STAGING_SUFFIXES: () => DEV_OR_STAGING_SUFFIXES,
  LEGACY_DEV_INSTANCE_SUFFIXES: () => LEGACY_DEV_INSTANCE_SUFFIXES,
  LOCAL_API_URL: () => LOCAL_API_URL,
  LOCAL_ENV_SUFFIXES: () => LOCAL_ENV_SUFFIXES,
  PROD_API_URL: () => PROD_API_URL,
  STAGING_API_URL: () => STAGING_API_URL,
  STAGING_ENV_SUFFIXES: () => STAGING_ENV_SUFFIXES,
  iconImageUrl: () => iconImageUrl
});
module.exports = __toCommonJS(constants_exports);
var LEGACY_DEV_INSTANCE_SUFFIXES = [".lcl.dev", ".lclstage.dev", ".lclclerk.com"];
var CURRENT_DEV_INSTANCE_SUFFIXES = [".accounts.dev", ".accountsstage.dev", ".accounts.lclclerk.com"];
var DEV_OR_STAGING_SUFFIXES = [
  ".lcl.dev",
  ".stg.dev",
  ".lclstage.dev",
  ".stgstage.dev",
  ".dev.lclclerk.com",
  ".stg.lclclerk.com",
  ".accounts.lclclerk.com",
  "accountsstage.dev",
  "accounts.dev"
];
var LOCAL_ENV_SUFFIXES = [".lcl.dev", "lclstage.dev", ".lclclerk.com", ".accounts.lclclerk.com"];
var STAGING_ENV_SUFFIXES = [".accountsstage.dev"];
var LOCAL_API_URL = "https://api.lclclerk.com";
var STAGING_API_URL = "https://api.clerkstage.dev";
var PROD_API_URL = "https://api.clerk.com";
function iconImageUrl(id, format = "svg") {
  return `https://img.clerk.com/static/${id}.${format}`;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  CURRENT_DEV_INSTANCE_SUFFIXES,
  DEV_OR_STAGING_SUFFIXES,
  LEGACY_DEV_INSTANCE_SUFFIXES,
  LOCAL_API_URL,
  LOCAL_ENV_SUFFIXES,
  PROD_API_URL,
  STAGING_API_URL,
  STAGING_ENV_SUFFIXES,
  iconImageUrl
});
//# sourceMappingURL=constants.js.map