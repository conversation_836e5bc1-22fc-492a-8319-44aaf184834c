{"version": 3, "sources": ["../src/apiUrlFromPublishableKey.ts"], "sourcesContent": ["import {\n  LEGACY_DEV_INSTANCE_SUFFIXES,\n  LOCAL_API_URL,\n  LOCAL_ENV_SUFFIXES,\n  PROD_API_URL,\n  STAGING_API_URL,\n  STAGING_ENV_SUFFIXES,\n} from './constants';\nimport { parsePublishableKey } from './keys';\n\n/**\n * Get the correct API url based on the publishable key.\n *\n * @param publishableKey - The publishable key to parse.\n * @returns One of Clerk's API URLs.\n */\nexport const apiUrlFromPublishableKey = (publishableKey: string) => {\n  const frontendApi = parsePublishableKey(publishableKey)?.frontendApi;\n\n  if (frontendApi?.startsWith('clerk.') && LEGACY_DEV_INSTANCE_SUFFIXES.some(suffix => frontendApi?.endsWith(suffix))) {\n    return PROD_API_URL;\n  }\n\n  if (LOCAL_ENV_SUFFIXES.some(suffix => frontendApi?.endsWith(suffix))) {\n    return LOCAL_API_URL;\n  }\n  if (STAGING_ENV_SUFFIXES.some(suffix => frontendApi?.endsWith(suffix))) {\n    return STAGING_API_URL;\n  }\n  return PROD_API_URL;\n};\n"], "mappings": ";;;;;;;;;;;;;AAgBO,IAAM,2BAA2B,CAAC,mBAA2B;AAClE,QAAM,cAAc,oBAAoB,cAAc,GAAG;AAEzD,MAAI,aAAa,WAAW,QAAQ,KAAK,6BAA6B,KAAK,YAAU,aAAa,SAAS,MAAM,CAAC,GAAG;AACnH,WAAO;AAAA,EACT;AAEA,MAAI,mBAAmB,KAAK,YAAU,aAAa,SAAS,MAAM,CAAC,GAAG;AACpE,WAAO;AAAA,EACT;AACA,MAAI,qBAAqB,KAAK,YAAU,aAAa,SAAS,MAAM,CAAC,GAAG;AACtE,WAAO;AAAA,EACT;AACA,SAAO;AACT;", "names": []}