{"version": 3, "sources": ["../src/underscore.ts"], "sourcesContent": ["/**\n * Convert words to a sentence.\n *\n * @param items - An array of words to be joined.\n * @returns A string with the items joined by a comma and the last item joined by \", or\".\n */\nexport const toSentence = (items: string[]): string => {\n  // TODO: Once Safari supports it, use Intl.ListFormat\n  if (items.length == 0) {\n    return '';\n  }\n  if (items.length == 1) {\n    return items[0];\n  }\n  let sentence = items.slice(0, -1).join(', ');\n  sentence += `, or ${items.slice(-1)}`;\n  return sentence;\n};\n\nconst IP_V4_ADDRESS_REGEX =\n  /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\n\n/**\n * Checks if a string is a valid IPv4 address.\n *\n * @returns True if the string is a valid IPv4 address, false otherwise.\n */\nexport function isIPV4Address(str: string | undefined | null): boolean {\n  return IP_V4_ADDRESS_REGEX.test(str || '');\n}\n\n/**\n * Converts the first character of a string to uppercase.\n *\n * @param str - The string to be converted.\n * @returns The modified string with the rest of the string unchanged.\n *\n * @example\n * ```ts\n * titleize('hello world') // 'Hello world'\n * ```\n */\nexport function titleize(str: string | undefined | null): string {\n  const s = str || '';\n  return s.charAt(0).toUpperCase() + s.slice(1);\n}\n\n/**\n * Converts a string from snake_case to camelCase.\n */\nexport function snakeToCamel(str: string | undefined): string {\n  return str ? str.replace(/([-_][a-z])/g, match => match.toUpperCase().replace(/-|_/, '')) : '';\n}\n\n/**\n * Converts a string from camelCase to snake_case.\n */\nexport function camelToSnake(str: string | undefined): string {\n  return str ? str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`) : '';\n}\n\nconst createDeepObjectTransformer = (transform: any) => {\n  const deepTransform = (obj: any): any => {\n    if (!obj) {\n      return obj;\n    }\n\n    if (Array.isArray(obj)) {\n      return obj.map(el => {\n        if (typeof el === 'object' || Array.isArray(el)) {\n          return deepTransform(el);\n        }\n        return el;\n      });\n    }\n\n    const copy = { ...obj };\n    const keys = Object.keys(copy);\n    for (const oldName of keys) {\n      const newName = transform(oldName.toString());\n      if (newName !== oldName) {\n        copy[newName] = copy[oldName];\n        delete copy[oldName];\n      }\n      if (typeof copy[newName] === 'object') {\n        copy[newName] = deepTransform(copy[newName]);\n      }\n    }\n    return copy;\n  };\n\n  return deepTransform;\n};\n\n/**\n * Transforms camelCased objects/ arrays to snake_cased.\n * This function recursively traverses all objects and arrays of the passed value\n * camelCased keys are removed.\n *\n * @function\n */\nexport const deepCamelToSnake = createDeepObjectTransformer(camelToSnake);\n\n/**\n * Transforms snake_cased objects/ arrays to camelCased.\n * This function recursively traverses all objects and arrays of the passed value\n * camelCased keys are removed.\n *\n * @function\n */\nexport const deepSnakeToCamel = createDeepObjectTransformer(snakeToCamel);\n\n/**\n * A function to determine if a value is truthy.\n *\n * @returns True for `true`, true, positive numbers. False for `false`, false, 0, negative integers and anything else.\n */\nexport function isTruthy(value: unknown): boolean {\n  // Return if Boolean\n  if (typeof value === `boolean`) {\n    return value;\n  }\n\n  // Return false if null or undefined\n  if (value === undefined || value === null) {\n    return false;\n  }\n\n  // If the String is true or false\n  if (typeof value === `string`) {\n    if (value.toLowerCase() === `true`) {\n      return true;\n    }\n\n    if (value.toLowerCase() === `false`) {\n      return false;\n    }\n  }\n\n  // Now check if it's a number\n  const number = parseInt(value as string, 10);\n  if (isNaN(number)) {\n    return false;\n  }\n\n  if (number > 0) {\n    return true;\n  }\n\n  // Default to false\n  return false;\n}\n\n/**\n * Get all non-undefined values from an object.\n */\nexport function getNonUndefinedValues<T extends object>(obj: T): Partial<T> {\n  return Object.entries(obj).reduce((acc, [key, value]) => {\n    if (value !== undefined) {\n      acc[key as keyof T] = value;\n    }\n    return acc;\n  }, {} as Partial<T>);\n}\n"], "mappings": ";AAMO,IAAM,aAAa,CAAC,UAA4B;AAErD,MAAI,MAAM,UAAU,GAAG;AACrB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,UAAU,GAAG;AACrB,WAAO,MAAM,CAAC;AAAA,EAChB;AACA,MAAI,WAAW,MAAM,MAAM,GAAG,EAAE,EAAE,KAAK,IAAI;AAC3C,cAAY,QAAQ,MAAM,MAAM,EAAE,CAAC;AACnC,SAAO;AACT;AAEA,IAAM,sBACJ;AAOK,SAAS,cAAc,KAAyC;AACrE,SAAO,oBAAoB,KAAK,OAAO,EAAE;AAC3C;AAaO,SAAS,SAAS,KAAwC;AAC/D,QAAM,IAAI,OAAO;AACjB,SAAO,EAAE,OAAO,CAAC,EAAE,YAAY,IAAI,EAAE,MAAM,CAAC;AAC9C;AAKO,SAAS,aAAa,KAAiC;AAC5D,SAAO,MAAM,IAAI,QAAQ,gBAAgB,WAAS,MAAM,YAAY,EAAE,QAAQ,OAAO,EAAE,CAAC,IAAI;AAC9F;AAKO,SAAS,aAAa,KAAiC;AAC5D,SAAO,MAAM,IAAI,QAAQ,UAAU,YAAU,IAAI,OAAO,YAAY,CAAC,EAAE,IAAI;AAC7E;AAEA,IAAM,8BAA8B,CAAC,cAAmB;AACtD,QAAM,gBAAgB,CAAC,QAAkB;AACvC,QAAI,CAAC,KAAK;AACR,aAAO;AAAA,IACT;AAEA,QAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,aAAO,IAAI,IAAI,QAAM;AACnB,YAAI,OAAO,OAAO,YAAY,MAAM,QAAQ,EAAE,GAAG;AAC/C,iBAAO,cAAc,EAAE;AAAA,QACzB;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAEA,UAAM,OAAO,EAAE,GAAG,IAAI;AACtB,UAAM,OAAO,OAAO,KAAK,IAAI;AAC7B,eAAW,WAAW,MAAM;AAC1B,YAAM,UAAU,UAAU,QAAQ,SAAS,CAAC;AAC5C,UAAI,YAAY,SAAS;AACvB,aAAK,OAAO,IAAI,KAAK,OAAO;AAC5B,eAAO,KAAK,OAAO;AAAA,MACrB;AACA,UAAI,OAAO,KAAK,OAAO,MAAM,UAAU;AACrC,aAAK,OAAO,IAAI,cAAc,KAAK,OAAO,CAAC;AAAA,MAC7C;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AASO,IAAM,mBAAmB,4BAA4B,YAAY;AASjE,IAAM,mBAAmB,4BAA4B,YAAY;AAOjE,SAAS,SAAS,OAAyB;AAEhD,MAAI,OAAO,UAAU,WAAW;AAC9B,WAAO;AAAA,EACT;AAGA,MAAI,UAAU,UAAa,UAAU,MAAM;AACzC,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI,MAAM,YAAY,MAAM,QAAQ;AAClC,aAAO;AAAA,IACT;AAEA,QAAI,MAAM,YAAY,MAAM,SAAS;AACnC,aAAO;AAAA,IACT;AAAA,EACF;AAGA,QAAM,SAAS,SAAS,OAAiB,EAAE;AAC3C,MAAI,MAAM,MAAM,GAAG;AACjB,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,GAAG;AACd,WAAO;AAAA,EACT;AAGA,SAAO;AACT;AAKO,SAAS,sBAAwC,KAAoB;AAC1E,SAAO,OAAO,QAAQ,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM;AACvD,QAAI,UAAU,QAAW;AACvB,UAAI,GAAc,IAAI;AAAA,IACxB;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAe;AACrB;", "names": []}