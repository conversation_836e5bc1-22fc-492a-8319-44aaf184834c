{"version": 3, "sources": ["../src/utils/allSettled.ts", "../src/utils/logErrorInDevMode.ts", "../src/utils/fastDeepMerge.ts"], "sourcesContent": ["/**\n * A ES6 compatible utility that implements `Promise.allSettled`\n * @internal\n */\nexport function allSettled<T>(\n  iterable: Iterable<Promise<T>>,\n): Promise<({ status: 'fulfilled'; value: T } | { status: 'rejected'; reason: any })[]> {\n  const promises = Array.from(iterable).map(p =>\n    p.then(\n      value => ({ status: 'fulfilled', value }) as const,\n      reason => ({ status: 'rejected', reason }) as const,\n    ),\n  );\n  return Promise.all(promises);\n}\n", "import { isDevelopmentEnvironment } from './runtimeEnvironment';\n\nexport const logErrorInDevMode = (message: string) => {\n  if (isDevelopmentEnvironment()) {\n    console.error(`Clerk: ${message}`);\n  }\n};\n", "/**\n * Merges 2 objects without creating new object references\n * The merged props will appear on the `target` object\n * If `target` already has a value for a given key it will not be overwritten\n */\nexport const fastDeepMergeAndReplace = (\n  source: Record<any, any> | undefined | null,\n  target: Record<any, any> | undefined | null,\n) => {\n  if (!source || !target) {\n    return;\n  }\n\n  for (const key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key) && source[key] !== null && typeof source[key] === `object`) {\n      if (target[key] === undefined) {\n        target[key] = new (Object.getPrototypeOf(source[key]).constructor)();\n      }\n      fastDeepMergeAndReplace(source[key], target[key]);\n    } else if (Object.prototype.hasOwnProperty.call(source, key)) {\n      target[key] = source[key];\n    }\n  }\n};\n\nexport const fastDeepMergeAndKeep = (\n  source: Record<any, any> | undefined | null,\n  target: Record<any, any> | undefined | null,\n) => {\n  if (!source || !target) {\n    return;\n  }\n\n  for (const key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key) && source[key] !== null && typeof source[key] === `object`) {\n      if (target[key] === undefined) {\n        target[key] = new (Object.getPrototypeOf(source[key]).constructor)();\n      }\n      fastDeepMergeAndKeep(source[key], target[key]);\n    } else if (Object.prototype.hasOwnProperty.call(source, key) && target[key] === undefined) {\n      target[key] = source[key];\n    }\n  }\n};\n"], "mappings": ";;;;;AAIO,SAAS,WACd,UACsF;AACtF,QAAM,WAAW,MAAM,KAAK,QAAQ,EAAE;AAAA,IAAI,OACxC,EAAE;AAAA,MACA,YAAU,EAAE,QAAQ,aAAa,MAAM;AAAA,MACvC,aAAW,EAAE,QAAQ,YAAY,OAAO;AAAA,IAC1C;AAAA,EACF;AACA,SAAO,QAAQ,IAAI,QAAQ;AAC7B;;;ACZO,IAAM,oBAAoB,CAAC,YAAoB;AACpD,MAAI,yBAAyB,GAAG;AAC9B,YAAQ,MAAM,UAAU,OAAO,EAAE;AAAA,EACnC;AACF;;;ACDO,IAAM,0BAA0B,CACrC,QACA,WACG;AACH,MAAI,CAAC,UAAU,CAAC,QAAQ;AACtB;AAAA,EACF;AAEA,aAAW,OAAO,QAAQ;AACxB,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,KAAK,OAAO,GAAG,MAAM,QAAQ,OAAO,OAAO,GAAG,MAAM,UAAU;AAChH,UAAI,OAAO,GAAG,MAAM,QAAW;AAC7B,eAAO,GAAG,IAAI,KAAK,OAAO,eAAe,OAAO,GAAG,CAAC,GAAE,YAAa;AAAA,MACrE;AACA,8BAAwB,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;AAAA,IAClD,WAAW,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAC5D,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AACF;AAEO,IAAM,uBAAuB,CAClC,QACA,WACG;AACH,MAAI,CAAC,UAAU,CAAC,QAAQ;AACtB;AAAA,EACF;AAEA,aAAW,OAAO,QAAQ;AACxB,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,KAAK,OAAO,GAAG,MAAM,QAAQ,OAAO,OAAO,GAAG,MAAM,UAAU;AAChH,UAAI,OAAO,GAAG,MAAM,QAAW;AAC7B,eAAO,GAAG,IAAI,KAAK,OAAO,eAAe,OAAO,GAAG,CAAC,GAAE,YAAa;AAAA,MACrE;AACA,2BAAqB,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;AAAA,IAC/C,WAAW,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,KAAK,OAAO,GAAG,MAAM,QAAW;AACzF,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AACF;", "names": []}