{"version": 3, "sources": ["../src/netlifyCacheHandler.ts"], "sourcesContent": ["/* eslint-disable turbo/no-undeclared-env-vars */\nimport { isDevelopmentFromPublishableKey } from './keys';\n\n/**\n * Cache busting parameter for Netlify to prevent cached responses\n * during handshake flows with Clerk development instances.\n *\n * Note: This query parameter will be removed in the \"@clerk/clerk-js\" package.\n *\n * @internal\n */\nexport const CLERK_NETLIFY_CACHE_BUST_PARAM = '__clerk_netlify_cache_bust';\n\n/**\n * Returns true if running in a Netlify environment.\n * Checks for Netlify-specific environment variables in process.env.\n * Safe for browser and non-Node environments.\n */\nfunction isNetlifyRuntime(): boolean {\n  if (typeof process === 'undefined' || !process.env) {\n    return false;\n  }\n\n  return (\n    <PERSON><PERSON>an(process.env.NETLIFY) ||\n    Boolean(process.env.NETLIFY_FUNCTIONS_TOKEN) ||\n    (typeof process.env.URL === 'string' && process.env.URL.endsWith('netlify.app'))\n  );\n}\n\n/**\n * Prevents infinite redirects in Netlify's functions by adding a cache bust parameter\n * to the original redirect URL. This ensures that <PERSON><PERSON> doesn't serve a cached response\n * during the handshake flow.\n *\n * The issue happens only on Clerk development instances running on Netlify. This is\n * a workaround until we find a better solution.\n *\n * See https://answers.netlify.com/t/cache-handling-recommendation-for-authentication-handshake-redirects/143969/1.\n *\n * @internal\n */\nexport function handleNetlifyCacheInDevInstance({\n  locationHeader,\n  requestStateHeaders,\n  publishableKey,\n}: {\n  locationHeader: string;\n  requestStateHeaders: Headers;\n  publishableKey: string;\n}) {\n  const isOnNetlify = isNetlifyRuntime();\n  const isDevelopmentInstance = isDevelopmentFromPublishableKey(publishableKey);\n\n  if (isOnNetlify && isDevelopmentInstance) {\n    const hasHandshakeQueryParam = locationHeader.includes('__clerk_handshake');\n    // If location header is the original URL before the handshake flow, add cache bust param\n    // The param should be removed in clerk-js\n    if (!hasHandshakeQueryParam) {\n      const url = new URL(locationHeader);\n      url.searchParams.append(CLERK_NETLIFY_CACHE_BUST_PARAM, Date.now().toString());\n      requestStateHeaders.set('Location', url.toString());\n    }\n  }\n}\n"], "mappings": ";;;;;AAWO,IAAM,iCAAiC;AAO9C,SAAS,mBAA4B;AACnC,MAAI,OAAO,YAAY,eAAe,CAAC,QAAQ,KAAK;AAClD,WAAO;AAAA,EACT;AAEA,SACE,QAAQ,QAAQ,IAAI,OAAO,KAC3B,QAAQ,QAAQ,IAAI,uBAAuB,KAC1C,OAAO,QAAQ,IAAI,QAAQ,YAAY,QAAQ,IAAI,IAAI,SAAS,aAAa;AAElF;AAcO,SAAS,gCAAgC;AAAA,EAC9C;AAAA,EACA;AAAA,EACA;AACF,GAIG;AACD,QAAM,cAAc,iBAAiB;AACrC,QAAM,wBAAwB,gCAAgC,cAAc;AAE5E,MAAI,eAAe,uBAAuB;AACxC,UAAM,yBAAyB,eAAe,SAAS,mBAAmB;AAG1E,QAAI,CAAC,wBAAwB;AAC3B,YAAM,MAAM,IAAI,IAAI,cAAc;AAClC,UAAI,aAAa,OAAO,gCAAgC,KAAK,IAAI,EAAE,SAAS,CAAC;AAC7E,0BAAoB,IAAI,YAAY,IAAI,SAAS,CAAC;AAAA,IACpD;AAAA,EACF;AACF;", "names": []}