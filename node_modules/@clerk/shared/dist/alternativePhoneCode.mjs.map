{"version": 3, "sources": ["../src/alternativePhoneCode.ts"], "sourcesContent": ["import type { PhoneCodeChannelData } from '@clerk/types';\n\nexport const ALTERNATIVE_PHONE_CODE_PROVIDERS: PhoneCodeChannelData[] = [\n  {\n    channel: 'whatsapp',\n    name: 'WhatsApp',\n  },\n];\n\nexport const getAlternativePhoneCodeProviderData = (channel?: string): PhoneCodeChannelData | null => {\n  if (!channel) {\n    return null;\n  }\n  return ALTERNATIVE_PHONE_CODE_PROVIDERS.find(p => p.channel === channel) || null;\n};\n"], "mappings": ";;;AAEO,IAAM,mCAA2D;AAAA,EACtE;AAAA,IACE,SAAS;AAAA,IACT,MAAM;AAAA,EACR;AACF;AAEO,IAAM,sCAAsC,CAAC,YAAkD;AACpG,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACT;AACA,SAAO,iCAAiC,KAAK,OAAK,EAAE,YAAY,OAAO,KAAK;AAC9E;", "names": []}