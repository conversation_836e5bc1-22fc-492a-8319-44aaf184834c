{"version": 3, "sources": ["../src/devBrowser.ts"], "sourcesContent": ["export const DEV_BROWSER_JWT_KEY = '__clerk_db_jwt';\nexport const DEV_BROWSER_JWT_HEADER = 'Clerk-Db-Jwt';\n\n// Sets the dev_browser JWT in the hash or the search\nexport function setDevBrowserJWTInURL(url: URL, jwt: string): URL {\n  const resultURL = new URL(url);\n\n  // extract & strip existing jwt from search\n  const jwtFromSearch = resultURL.searchParams.get(DEV_BROWSER_JWT_KEY);\n  resultURL.searchParams.delete(DEV_BROWSER_JWT_KEY);\n\n  // Existing jwt takes precedence\n  const jwtToSet = jwtFromSearch || jwt;\n\n  if (jwtToSet) {\n    resultURL.searchParams.set(DEV_BROWSER_JWT_KEY, jwtToSet);\n  }\n\n  return resultURL;\n}\n\n/**\n * Gets the __clerk_db_jwt JWT from either the hash or the search\n * Side effect:\n * Removes __clerk_db_jwt JWT from the URL (hash and searchParams) and updates the browser history\n */\nexport function extractDevBrowserJWTFromURL(url: URL): string {\n  const jwt = readDevBrowserJwtFromSearchParams(url);\n  const cleanUrl = removeDevBrowserJwt(url);\n  if (cleanUrl.href !== url.href && typeof globalThis.history !== 'undefined') {\n    globalThis.history.replaceState(null, '', removeDevBrowserJwt(url));\n  }\n  return jwt;\n}\n\nconst readDevBrowserJwtFromSearchParams = (url: URL) => {\n  return url.searchParams.get(DEV_BROWSER_JWT_KEY) || '';\n};\n\nconst removeDevBrowserJwt = (url: URL) => {\n  return removeDevBrowserJwtFromURLSearchParams(removeLegacyDevBrowserJwt(url));\n};\n\nconst removeDevBrowserJwtFromURLSearchParams = (_url: URL) => {\n  const url = new URL(_url);\n  url.searchParams.delete(DEV_BROWSER_JWT_KEY);\n  return url;\n};\n\n/**\n * Removes the __clerk_db_jwt JWT from the URL hash, as well as\n * the legacy __dev_session JWT from the URL searchParams\n * We no longer need to use this value, however, we should remove it from the URL\n * Existing v4 apps will write the JWT to the hash and the search params in order to ensure\n * backwards compatibility with older v4 apps.\n * The only use case where this is needed now is when a user upgrades to clerk@5 locally\n * without changing the component's version on their dashboard.\n * In this scenario, the AP@4 -> localhost@5 redirect will still have the JWT in the hash,\n * in which case we need to remove it.\n */\nconst removeLegacyDevBrowserJwt = (_url: URL) => {\n  const DEV_BROWSER_JWT_MARKER_REGEXP = /__clerk_db_jwt\\[(.*)\\]/;\n  const DEV_BROWSER_JWT_LEGACY_KEY = '__dev_session';\n  const url = new URL(_url);\n  url.searchParams.delete(DEV_BROWSER_JWT_LEGACY_KEY);\n  url.hash = decodeURI(url.hash).replace(DEV_BROWSER_JWT_MARKER_REGEXP, '');\n  if (url.href.endsWith('#')) {\n    url.hash = '';\n  }\n  return url;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAO,IAAM,sBAAsB;AAC5B,IAAM,yBAAyB;AAG/B,SAAS,sBAAsB,KAAU,KAAkB;AAChE,QAAM,YAAY,IAAI,IAAI,GAAG;AAG7B,QAAM,gBAAgB,UAAU,aAAa,IAAI,mBAAmB;AACpE,YAAU,aAAa,OAAO,mBAAmB;AAGjD,QAAM,WAAW,iBAAiB;AAElC,MAAI,UAAU;AACZ,cAAU,aAAa,IAAI,qBAAqB,QAAQ;AAAA,EAC1D;AAEA,SAAO;AACT;AAOO,SAAS,4BAA4B,KAAkB;AAC5D,QAAM,MAAM,kCAAkC,GAAG;AACjD,QAAM,WAAW,oBAAoB,GAAG;AACxC,MAAI,SAAS,SAAS,IAAI,QAAQ,OAAO,WAAW,YAAY,aAAa;AAC3E,eAAW,QAAQ,aAAa,MAAM,IAAI,oBAAoB,GAAG,CAAC;AAAA,EACpE;AACA,SAAO;AACT;AAEA,IAAM,oCAAoC,CAAC,QAAa;AACtD,SAAO,IAAI,aAAa,IAAI,mBAAmB,KAAK;AACtD;AAEA,IAAM,sBAAsB,CAAC,QAAa;AACxC,SAAO,uCAAuC,0BAA0B,GAAG,CAAC;AAC9E;AAEA,IAAM,yCAAyC,CAAC,SAAc;AAC5D,QAAM,MAAM,IAAI,IAAI,IAAI;AACxB,MAAI,aAAa,OAAO,mBAAmB;AAC3C,SAAO;AACT;AAaA,IAAM,4BAA4B,CAAC,SAAc;AAC/C,QAAM,gCAAgC;AACtC,QAAM,6BAA6B;AACnC,QAAM,MAAM,IAAI,IAAI,IAAI;AACxB,MAAI,aAAa,OAAO,0BAA0B;AAClD,MAAI,OAAO,UAAU,IAAI,IAAI,EAAE,QAAQ,+BAA+B,EAAE;AACxE,MAAI,IAAI,KAAK,SAAS,GAAG,GAAG;AAC1B,QAAI,OAAO;AAAA,EACb;AACA,SAAO;AACT;", "names": []}