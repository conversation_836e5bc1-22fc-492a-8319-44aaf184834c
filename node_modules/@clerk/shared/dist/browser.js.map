{"version": 3, "sources": ["../src/browser.ts"], "sourcesContent": ["/**\n * Checks if the window object is defined. You can also use this to check if something is happening on the client side.\n * @returns {boolean}\n */\nexport function inBrowser(): boolean {\n  return typeof window !== 'undefined';\n}\n\nconst botAgents = [\n  'bot',\n  'spider',\n  'crawl',\n  'APIs-Google',\n  'AdsBot',\n  'Googlebot',\n  'mediapartners',\n  'Google Favicon',\n  'FeedFetcher',\n  'Google-Read-Aloud',\n  'DuplexWeb-Google',\n  'googleweblight',\n  'bing',\n  'yandex',\n  'baidu',\n  'duckduck',\n  'yahoo',\n  'ecosia',\n  'ia_archiver',\n  'facebook',\n  'instagram',\n  'pinterest',\n  'reddit',\n  'slack',\n  'twitter',\n  'whatsapp',\n  'youtube',\n  'semrush',\n];\nconst botAgentRegex = new RegExp(botAgents.join('|'), 'i');\n\n/**\n * Checks if the user agent is a bot.\n * @param userAgent - Any user agent string\n * @returns {boolean}\n */\nexport function userAgentIsRobot(userAgent: string): boolean {\n  return !userAgent ? false : botAgentRegex.test(userAgent);\n}\n\n/**\n * Checks if the current environment is a browser and the user agent is not a bot.\n * @returns {boolean}\n */\nexport function isValidBrowser(): boolean {\n  const navigator = inBrowser() ? window?.navigator : null;\n  if (!navigator) {\n    return false;\n  }\n  return !userAgentIsRobot(navigator?.userAgent) && !navigator?.webdriver;\n}\n\n/**\n * Checks if the current environment is a browser and if the navigator is online.\n * @returns {boolean}\n */\nexport function isBrowserOnline(): boolean {\n  const navigator = inBrowser() ? window?.navigator : null;\n  if (!navigator) {\n    return false;\n  }\n\n  const isNavigatorOnline = navigator?.onLine;\n\n  // Being extra safe with the experimental `connection` property, as it is not defined in all browsers\n  // https://developer.mozilla.org/en-US/docs/Web/API/Navigator/connection#browser_compatibility\n  // @ts-ignore\n  const isExperimentalConnectionOnline = navigator?.connection?.rtt !== 0 && navigator?.connection?.downlink !== 0;\n  return isExperimentalConnectionOnline && isNavigatorOnline;\n}\n\n/**\n * Runs `isBrowserOnline` and `isValidBrowser` to check if the current environment is a valid browser and if the navigator is online.\n * @returns {boolean}\n */\nexport function isValidBrowserOnline(): boolean {\n  return isBrowserOnline() && isValidBrowser();\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIO,SAAS,YAAqB;AACnC,SAAO,OAAO,WAAW;AAC3B;AAEA,IAAM,YAAY;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,gBAAgB,IAAI,OAAO,UAAU,KAAK,GAAG,GAAG,GAAG;AAOlD,SAAS,iBAAiB,WAA4B;AAC3D,SAAO,CAAC,YAAY,QAAQ,cAAc,KAAK,SAAS;AAC1D;AAMO,SAAS,iBAA0B;AACxC,QAAM,YAAY,UAAU,IAAI,QAAQ,YAAY;AACpD,MAAI,CAAC,WAAW;AACd,WAAO;AAAA,EACT;AACA,SAAO,CAAC,iBAAiB,WAAW,SAAS,KAAK,CAAC,WAAW;AAChE;AAMO,SAAS,kBAA2B;AACzC,QAAM,YAAY,UAAU,IAAI,QAAQ,YAAY;AACpD,MAAI,CAAC,WAAW;AACd,WAAO;AAAA,EACT;AAEA,QAAM,oBAAoB,WAAW;AAKrC,QAAM,iCAAiC,WAAW,YAAY,QAAQ,KAAK,WAAW,YAAY,aAAa;AAC/G,SAAO,kCAAkC;AAC3C;AAMO,SAAS,uBAAgC;AAC9C,SAAO,gBAAgB,KAAK,eAAe;AAC7C;", "names": []}