{"version": 3, "sources": ["../src/localStorageBroadcastChannel.ts"], "sourcesContent": ["type Listener<T> = (e: MessageEvent<T>) => void;\n\nconst KEY_PREFIX = '__lsbc__';\n\nexport class LocalStorageBroadcastChannel<E> {\n  private readonly eventTarget = window;\n  private readonly channelKey: string;\n\n  constructor(name: string) {\n    this.channelKey = KEY_PREFIX + name;\n    this.setupLocalStorageListener();\n  }\n\n  public postMessage = (data: E): void => {\n    if (typeof window === 'undefined') {\n      // Silently do nothing\n      return;\n    }\n\n    try {\n      window.localStorage.setItem(this.channelKey, JSON.stringify(data));\n      window.localStorage.removeItem(this.channelKey);\n    } catch {\n      // Silently do nothing\n    }\n  };\n\n  public addEventListener = (eventName: 'message', listener: Listener<E>): void => {\n    this.eventTarget.addEventListener(this.prefixEventName(eventName), e => {\n      listener(e as MessageEvent);\n    });\n  };\n\n  private setupLocalStorageListener = () => {\n    const notifyListeners = (e: StorageEvent) => {\n      if (e.key !== this.channelKey || !e.newValue) {\n        return;\n      }\n\n      try {\n        const data = JSON.parse(e.newValue || '');\n        const event = new MessageEvent(this.prefixEventName('message'), {\n          data,\n        });\n        this.eventTarget.dispatchEvent(event);\n      } catch {\n        //\n      }\n    };\n\n    window.addEventListener('storage', notifyListeners);\n  };\n\n  private prefixEventName(eventName: string): string {\n    return this.channelKey + eventName;\n  }\n}\n"], "mappings": ";AAEA,IAAM,aAAa;AAEZ,IAAM,+BAAN,MAAsC;AAAA,EAI3C,YAAY,MAAc;AAH1B,SAAiB,cAAc;AAQ/B,SAAO,cAAc,CAAC,SAAkB;AACtC,UAAI,OAAO,WAAW,aAAa;AAEjC;AAAA,MACF;AAEA,UAAI;AACF,eAAO,aAAa,QAAQ,KAAK,YAAY,KAAK,UAAU,IAAI,CAAC;AACjE,eAAO,aAAa,WAAW,KAAK,UAAU;AAAA,MAChD,QAAQ;AAAA,MAER;AAAA,IACF;AAEA,SAAO,mBAAmB,CAAC,WAAsB,aAAgC;AAC/E,WAAK,YAAY,iBAAiB,KAAK,gBAAgB,SAAS,GAAG,OAAK;AACtE,iBAAS,CAAiB;AAAA,MAC5B,CAAC;AAAA,IACH;AAEA,SAAQ,4BAA4B,MAAM;AACxC,YAAM,kBAAkB,CAAC,MAAoB;AAC3C,YAAI,EAAE,QAAQ,KAAK,cAAc,CAAC,EAAE,UAAU;AAC5C;AAAA,QACF;AAEA,YAAI;AACF,gBAAM,OAAO,KAAK,MAAM,EAAE,YAAY,EAAE;AACxC,gBAAM,QAAQ,IAAI,aAAa,KAAK,gBAAgB,SAAS,GAAG;AAAA,YAC9D;AAAA,UACF,CAAC;AACD,eAAK,YAAY,cAAc,KAAK;AAAA,QACtC,QAAQ;AAAA,QAER;AAAA,MACF;AAEA,aAAO,iBAAiB,WAAW,eAAe;AAAA,IACpD;AA1CE,SAAK,aAAa,aAAa;AAC/B,SAAK,0BAA0B;AAAA,EACjC;AAAA,EA0CQ,gBAAgB,WAA2B;AACjD,WAAO,KAAK,aAAa;AAAA,EAC3B;AACF;", "names": []}