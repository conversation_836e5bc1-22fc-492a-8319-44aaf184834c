{"version": 3, "sources": ["../src/cookie.ts"], "sourcesContent": ["import Cookies from 'js-cookie';\n\n/**\n * Creates helper methods for dealing with a specific cookie.\n *\n * @example\n * ```ts\n * const cookie = createCookieHandler('my_cookie')\n *\n * cookie.set('my_value');\n * cookie.get() // 'my_value';\n * cookie.remove()\n * ```\n */\nexport function createCookieHandler(cookieName: string) {\n  return {\n    get() {\n      return Cookies.get(cookieName);\n    },\n    /**\n     * Setting a cookie will use some defaults such as path being set to \"/\".\n     */\n    set(newValue: string, options: Cookies.CookieAttributes = {}): void {\n      Cookies.set(cookieName, newValue, options);\n    },\n    /**\n     * On removing a cookie, you have to pass the exact same path/domain attributes used to set it initially\n     * > IMPORTANT! When deleting a cookie and you're not relying on the default attributes, you must pass the exact same path, domain, secure and sameSite attributes that were used to set the cookie.\n     *\n     * @see https://github.com/js-cookie/js-cookie#basic-usage\n     */\n    remove(cookieAttributes?: Cookies.CookieAttributes) {\n      Cookies.remove(cookieName, cookieAttributes);\n    },\n  };\n}\n"], "mappings": ";;;AAAA,OAAO,aAAa;AAcb,SAAS,oBAAoB,YAAoB;AACtD,SAAO;AAAA,IACL,MAAM;AACJ,aAAO,QAAQ,IAAI,UAAU;AAAA,IAC/B;AAAA;AAAA;AAAA;AAAA,IAIA,IAAI,UAAkB,UAAoC,CAAC,GAAS;AAClE,cAAQ,IAAI,YAAY,UAAU,OAAO;AAAA,IAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,OAAO,kBAA6C;AAClD,cAAQ,OAAO,YAAY,gBAAgB;AAAA,IAC7C;AAAA,EACF;AACF;", "names": []}