{"version": 3, "sources": ["../src/organization.ts"], "sourcesContent": ["import type { OrganizationMembershipResource } from '@clerk/types';\n\n/**\n * Finds the organization membership for a given organization ID from a list of memberships\n * @param organizationMemberships - Array of organization memberships to search through\n * @param organizationId - ID of the organization to find the membership for\n * @returns The matching organization membership or undefined if not found\n */\nexport function getCurrentOrganizationMembership(\n  organizationMemberships: OrganizationMembershipResource[],\n  organizationId: string,\n) {\n  return organizationMemberships.find(\n    organizationMembership => organizationMembership.organization.id === organizationId,\n  );\n}\n"], "mappings": ";AAQO,SAAS,iCACd,yBACA,gBACA;AACA,SAAO,wBAAwB;AAAA,IAC7B,4BAA0B,uBAAuB,aAAa,OAAO;AAAA,EACvE;AACF;", "names": []}