{"version": 3, "sources": ["../src/buildAccountsBaseUrl.ts"], "sourcesContent": ["/**\n * Builds a full origin string pointing to the Account Portal for the given frontend API.\n */\nexport function buildAccountsBaseUrl(frontendApi?: string): string {\n  if (!frontendApi) {\n    return '';\n  }\n\n  // convert url from FAPI to accounts for Kima and legacy (prod & dev) instances\n  const accountsBaseUrl = frontendApi\n    // staging accounts\n    .replace(/clerk\\.accountsstage\\./, 'accountsstage.')\n    .replace(/clerk\\.accounts\\.|clerk\\./, 'accounts.');\n  return `https://${accountsBaseUrl}`;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAGO,SAAS,qBAAqB,aAA8B;AACjE,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AAGA,QAAM,kBAAkB,YAErB,QAAQ,0BAA0B,gBAAgB,EAClD,QAAQ,6BAA6B,WAAW;AACnD,SAAO,WAAW,eAAe;AACnC;", "names": []}