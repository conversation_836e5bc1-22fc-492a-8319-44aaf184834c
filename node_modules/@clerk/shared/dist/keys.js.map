{"version": 3, "sources": ["../src/keys.ts", "../src/constants.ts", "../src/isomorphicAtob.ts", "../src/isomorphicBtoa.ts"], "sourcesContent": ["import type { Publishable<PERSON><PERSON> } from '@clerk/types';\n\nimport { DEV_OR_STAGING_SUFFIXES, LEGACY_DEV_INSTANCE_SUFFIXES } from './constants';\nimport { isomorphicAtob } from './isomorphicAtob';\nimport { isomorphicBtoa } from './isomorphicBtoa';\n\n/**\n * Configuration options for parsing publishable keys.\n */\ntype ParsePublishableKeyOptions = {\n  /** Whether to throw an error if parsing fails */\n  fatal?: boolean;\n  /** Custom domain to use for satellite instances */\n  domain?: string;\n  /** Proxy URL to use instead of the decoded frontend API */\n  proxyUrl?: string;\n  /** Whether this is a satellite instance */\n  isSatellite?: boolean;\n};\n\n/** Prefix used for production publishable keys */\nconst PUBLISHABLE_KEY_LIVE_PREFIX = 'pk_live_';\n\n/** Prefix used for development publishable keys */\nconst PUBLISHABLE_KEY_TEST_PREFIX = 'pk_test_';\n\n/**\n * Regular expression that matches development frontend API keys.\n * Matches patterns like: foo-bar-13.clerk.accounts.dev.\n */\nconst PUBLISHABLE_FRONTEND_API_DEV_REGEX = /^(([a-z]+)-){2}([0-9]{1,2})\\.clerk\\.accounts([a-z.]*)(dev|com)$/i;\n\n/**\n * Converts a frontend API URL into a base64-encoded publishable key.\n *\n * @param frontendApi - The frontend API URL (e.g., 'clerk.example.com').\n * @returns A base64-encoded publishable key with appropriate prefix (pk_live_ or pk_test_).\n */\nexport function buildPublishableKey(frontendApi: string): string {\n  const isDevKey =\n    PUBLISHABLE_FRONTEND_API_DEV_REGEX.test(frontendApi) ||\n    (frontendApi.startsWith('clerk.') && LEGACY_DEV_INSTANCE_SUFFIXES.some(s => frontendApi.endsWith(s)));\n  const keyPrefix = isDevKey ? PUBLISHABLE_KEY_TEST_PREFIX : PUBLISHABLE_KEY_LIVE_PREFIX;\n  return `${keyPrefix}${isomorphicBtoa(`${frontendApi}$`)}`;\n}\n\n/**\n * Validates that a decoded publishable key has the correct format.\n * The decoded value should be a frontend API followed by exactly one '$' at the end.\n *\n * @param decoded - The decoded publishable key string to validate.\n * @returns `true` if the decoded key has valid format, `false` otherwise.\n */\nfunction isValidDecodedPublishableKey(decoded: string): boolean {\n  if (!decoded.endsWith('$')) {\n    return false;\n  }\n\n  const withoutTrailing = decoded.slice(0, -1);\n  if (withoutTrailing.includes('$')) {\n    return false;\n  }\n\n  return withoutTrailing.includes('.');\n}\n\nexport function parsePublishableKey(\n  key: string | undefined,\n  options: ParsePublishableKeyOptions & { fatal: true },\n): PublishableKey;\nexport function parsePublishableKey(\n  key: string | undefined,\n  options?: ParsePublishableKeyOptions,\n): PublishableKey | null;\n/**\n * Parses and validates a publishable key, extracting the frontend API and instance type.\n *\n * @param key - The publishable key to parse.\n * @param options - Configuration options for parsing.\n * @param options.fatal\n * @param options.domain\n * @param options.proxyUrl\n * @param options.isSatellite\n * @returns Parsed publishable key object with instanceType and frontendApi, or null if invalid.\n *\n * @throws {Error} When options.fatal is true and key is missing or invalid.\n */\nexport function parsePublishableKey(\n  key: string | undefined,\n  options: { fatal?: boolean; domain?: string; proxyUrl?: string; isSatellite?: boolean } = {},\n): PublishableKey | null {\n  key = key || '';\n\n  if (!key || !isPublishableKey(key)) {\n    if (options.fatal && !key) {\n      throw new Error(\n        'Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys',\n      );\n    }\n    if (options.fatal && !isPublishableKey(key)) {\n      throw new Error('Publishable key not valid.');\n    }\n    return null;\n  }\n\n  const instanceType = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) ? 'production' : 'development';\n\n  let decodedFrontendApi: string;\n  try {\n    decodedFrontendApi = isomorphicAtob(key.split('_')[2]);\n  } catch {\n    if (options.fatal) {\n      throw new Error('Publishable key not valid: Failed to decode key.');\n    }\n    return null;\n  }\n\n  if (!isValidDecodedPublishableKey(decodedFrontendApi)) {\n    if (options.fatal) {\n      throw new Error('Publishable key not valid: Decoded key has invalid format.');\n    }\n    return null;\n  }\n\n  let frontendApi = decodedFrontendApi.slice(0, -1);\n\n  if (options.proxyUrl) {\n    frontendApi = options.proxyUrl;\n  } else if (instanceType !== 'development' && options.domain && options.isSatellite) {\n    frontendApi = `clerk.${options.domain}`;\n  }\n\n  return {\n    instanceType,\n    frontendApi,\n  };\n}\n\n/**\n * Checks if the provided key is a valid publishable key.\n *\n * @param key - The key to be checked. Defaults to an empty string if not provided.\n * @returns `true` if 'key' is a valid publishable key, `false` otherwise.\n */\nexport function isPublishableKey(key: string = '') {\n  try {\n    const hasValidPrefix = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) || key.startsWith(PUBLISHABLE_KEY_TEST_PREFIX);\n\n    if (!hasValidPrefix) {\n      return false;\n    }\n\n    const parts = key.split('_');\n    if (parts.length !== 3) {\n      return false;\n    }\n\n    const encodedPart = parts[2];\n    if (!encodedPart) {\n      return false;\n    }\n\n    const decoded = isomorphicAtob(encodedPart);\n    return isValidDecodedPublishableKey(decoded);\n  } catch {\n    return false;\n  }\n}\n\n/**\n * Creates a memoized cache for checking if URLs are development or staging environments.\n * Uses a Map to cache results for better performance on repeated checks.\n *\n * @returns An object with an isDevOrStagingUrl method that checks if a URL is dev/staging.\n */\nexport function createDevOrStagingUrlCache() {\n  const devOrStagingUrlCache = new Map<string, boolean>();\n\n  return {\n    /**\n     * Checks if a URL is a development or staging environment.\n     *\n     * @param url - The URL to check (string or URL object).\n     * @returns `true` if the URL is a development or staging environment, `false` otherwise.\n     */\n    isDevOrStagingUrl: (url: string | URL): boolean => {\n      if (!url) {\n        return false;\n      }\n\n      const hostname = typeof url === 'string' ? url : url.hostname;\n      let res = devOrStagingUrlCache.get(hostname);\n      if (res === undefined) {\n        res = DEV_OR_STAGING_SUFFIXES.some(s => hostname.endsWith(s));\n        devOrStagingUrlCache.set(hostname, res);\n      }\n      return res;\n    },\n  };\n}\n\n/**\n * Checks if a publishable key is for a development environment.\n * Supports both legacy format (test_) and new format (pk_test_).\n *\n * @param apiKey - The API key to check.\n * @returns `true` if the key is for development, `false` otherwise.\n */\nexport function isDevelopmentFromPublishableKey(apiKey: string): boolean {\n  return apiKey.startsWith('test_') || apiKey.startsWith('pk_test_');\n}\n\n/**\n * Checks if a publishable key is for a production environment.\n * Supports both legacy format (live_) and new format (pk_live_).\n *\n * @param apiKey - The API key to check.\n * @returns `true` if the key is for production, `false` otherwise.\n */\nexport function isProductionFromPublishableKey(apiKey: string): boolean {\n  return apiKey.startsWith('live_') || apiKey.startsWith('pk_live_');\n}\n\n/**\n * Checks if a secret key is for a development environment.\n * Supports both legacy format (test_) and new format (sk_test_).\n *\n * @param apiKey - The secret key to check.\n * @returns `true` if the key is for development, `false` otherwise.\n */\nexport function isDevelopmentFromSecretKey(apiKey: string): boolean {\n  return apiKey.startsWith('test_') || apiKey.startsWith('sk_test_');\n}\n\n/**\n * Checks if a secret key is for a production environment.\n * Supports both legacy format (live_) and new format (sk_live_).\n *\n * @param apiKey - The secret key to check.\n * @returns `true` if the key is for production, `false` otherwise.\n */\nexport function isProductionFromSecretKey(apiKey: string): boolean {\n  return apiKey.startsWith('live_') || apiKey.startsWith('sk_live_');\n}\n\n/**\n * Generates a unique cookie suffix based on the publishable key using SHA-1 hashing.\n * The suffix is base64-encoded and URL-safe (+ and / characters are replaced).\n *\n * @param publishableKey - The publishable key to generate suffix from.\n * @param subtle - The SubtleCrypto interface to use for hashing (defaults to globalThis.crypto.subtle).\n * @returns A promise that resolves to an 8-character URL-safe base64 string.\n */\nexport async function getCookieSuffix(\n  publishableKey: string,\n  subtle: SubtleCrypto = globalThis.crypto.subtle,\n): Promise<string> {\n  const data = new TextEncoder().encode(publishableKey);\n  const digest = await subtle.digest('sha-1', data);\n  const stringDigest = String.fromCharCode(...new Uint8Array(digest));\n  // Base 64 Encoding with URL and Filename Safe Alphabet: https://datatracker.ietf.org/doc/html/rfc4648#section-5\n  return isomorphicBtoa(stringDigest).replace(/\\+/gi, '-').replace(/\\//gi, '_').substring(0, 8);\n}\n\n/**\n * Creates a suffixed cookie name by appending the cookie suffix to the base name.\n * Used to create unique cookie names based on the publishable key.\n *\n * @param cookieName - The base cookie name.\n * @param cookieSuffix - The suffix to append (typically generated by getCookieSuffix).\n * @returns The suffixed cookie name in format: `${cookieName}_${cookieSuffix}`.\n */\nexport const getSuffixedCookieName = (cookieName: string, cookieSuffix: string): string => {\n  return `${cookieName}_${cookieSuffix}`;\n};\n", "export const LEGACY_DEV_INSTANCE_SUFFIXES = ['.lcl.dev', '.lclstage.dev', '.lclclerk.com'];\nexport const CURRENT_DEV_INSTANCE_SUFFIXES = ['.accounts.dev', '.accountsstage.dev', '.accounts.lclclerk.com'];\nexport const DEV_OR_STAGING_SUFFIXES = [\n  '.lcl.dev',\n  '.stg.dev',\n  '.lclstage.dev',\n  '.stgstage.dev',\n  '.dev.lclclerk.com',\n  '.stg.lclclerk.com',\n  '.accounts.lclclerk.com',\n  'accountsstage.dev',\n  'accounts.dev',\n];\nexport const LOCAL_ENV_SUFFIXES = ['.lcl.dev', 'lclstage.dev', '.lclclerk.com', '.accounts.lclclerk.com'];\nexport const STAGING_ENV_SUFFIXES = ['.accountsstage.dev'];\nexport const LOCAL_API_URL = 'https://api.lclclerk.com';\nexport const STAGING_API_URL = 'https://api.clerkstage.dev';\nexport const PROD_API_URL = 'https://api.clerk.com';\n\n/**\n * Returns the URL for a static image\n * using the new img.clerk.com service\n */\nexport function iconImageUrl(id: string, format: 'svg' | 'jpeg' = 'svg'): string {\n  return `https://img.clerk.com/static/${id}.${format}`;\n}\n", "/**\n * A function that decodes a string of data which has been encoded using base-64 encoding.\n * Uses `atob` if available, otherwise uses `<PERSON><PERSON><PERSON>` from `global`. If neither are available, returns the data as-is.\n */\nexport const isomorphicAtob = (data: string) => {\n  if (typeof atob !== 'undefined' && typeof atob === 'function') {\n    return atob(data);\n  } else if (typeof global !== 'undefined' && global.Buffer) {\n    return new global.Buffer(data, 'base64').toString();\n  }\n  return data;\n};\n", "export const isomorphicBtoa = (data: string) => {\n  if (typeof btoa !== 'undefined' && typeof btoa === 'function') {\n    return btoa(data);\n  } else if (typeof global !== 'undefined' && global.Buffer) {\n    return new global.Buffer(data).toString('base64');\n  }\n  return data;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAO,IAAM,+BAA+B,CAAC,YAAY,iBAAiB,eAAe;AAElF,IAAM,0BAA0B;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACRO,IAAM,iBAAiB,CAAC,SAAiB;AAC9C,MAAI,OAAO,SAAS,eAAe,OAAO,SAAS,YAAY;AAC7D,WAAO,KAAK,IAAI;AAAA,EAClB,WAAW,OAAO,WAAW,eAAe,OAAO,QAAQ;AACzD,WAAO,IAAI,OAAO,OAAO,MAAM,QAAQ,EAAE,SAAS;AAAA,EACpD;AACA,SAAO;AACT;;;ACXO,IAAM,iBAAiB,CAAC,SAAiB;AAC9C,MAAI,OAAO,SAAS,eAAe,OAAO,SAAS,YAAY;AAC7D,WAAO,KAAK,IAAI;AAAA,EAClB,WAAW,OAAO,WAAW,eAAe,OAAO,QAAQ;AACzD,WAAO,IAAI,OAAO,OAAO,IAAI,EAAE,SAAS,QAAQ;AAAA,EAClD;AACA,SAAO;AACT;;;AHcA,IAAM,8BAA8B;AAGpC,IAAM,8BAA8B;AAMpC,IAAM,qCAAqC;AAQpC,SAAS,oBAAoB,aAA6B;AAC/D,QAAM,WACJ,mCAAmC,KAAK,WAAW,KAClD,YAAY,WAAW,QAAQ,KAAK,6BAA6B,KAAK,OAAK,YAAY,SAAS,CAAC,CAAC;AACrG,QAAM,YAAY,WAAW,8BAA8B;AAC3D,SAAO,GAAG,SAAS,GAAG,eAAe,GAAG,WAAW,GAAG,CAAC;AACzD;AASA,SAAS,6BAA6B,SAA0B;AAC9D,MAAI,CAAC,QAAQ,SAAS,GAAG,GAAG;AAC1B,WAAO;AAAA,EACT;AAEA,QAAM,kBAAkB,QAAQ,MAAM,GAAG,EAAE;AAC3C,MAAI,gBAAgB,SAAS,GAAG,GAAG;AACjC,WAAO;AAAA,EACT;AAEA,SAAO,gBAAgB,SAAS,GAAG;AACrC;AAuBO,SAAS,oBACd,KACA,UAA0F,CAAC,GACpE;AACvB,QAAM,OAAO;AAEb,MAAI,CAAC,OAAO,CAAC,iBAAiB,GAAG,GAAG;AAClC,QAAI,QAAQ,SAAS,CAAC,KAAK;AACzB,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,QAAI,QAAQ,SAAS,CAAC,iBAAiB,GAAG,GAAG;AAC3C,YAAM,IAAI,MAAM,4BAA4B;AAAA,IAC9C;AACA,WAAO;AAAA,EACT;AAEA,QAAM,eAAe,IAAI,WAAW,2BAA2B,IAAI,eAAe;AAElF,MAAI;AACJ,MAAI;AACF,yBAAqB,eAAe,IAAI,MAAM,GAAG,EAAE,CAAC,CAAC;AAAA,EACvD,QAAQ;AACN,QAAI,QAAQ,OAAO;AACjB,YAAM,IAAI,MAAM,kDAAkD;AAAA,IACpE;AACA,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,6BAA6B,kBAAkB,GAAG;AACrD,QAAI,QAAQ,OAAO;AACjB,YAAM,IAAI,MAAM,4DAA4D;AAAA,IAC9E;AACA,WAAO;AAAA,EACT;AAEA,MAAI,cAAc,mBAAmB,MAAM,GAAG,EAAE;AAEhD,MAAI,QAAQ,UAAU;AACpB,kBAAc,QAAQ;AAAA,EACxB,WAAW,iBAAiB,iBAAiB,QAAQ,UAAU,QAAQ,aAAa;AAClF,kBAAc,SAAS,QAAQ,MAAM;AAAA,EACvC;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAQO,SAAS,iBAAiB,MAAc,IAAI;AACjD,MAAI;AACF,UAAM,iBAAiB,IAAI,WAAW,2BAA2B,KAAK,IAAI,WAAW,2BAA2B;AAEhH,QAAI,CAAC,gBAAgB;AACnB,aAAO;AAAA,IACT;AAEA,UAAM,QAAQ,IAAI,MAAM,GAAG;AAC3B,QAAI,MAAM,WAAW,GAAG;AACtB,aAAO;AAAA,IACT;AAEA,UAAM,cAAc,MAAM,CAAC;AAC3B,QAAI,CAAC,aAAa;AAChB,aAAO;AAAA,IACT;AAEA,UAAM,UAAU,eAAe,WAAW;AAC1C,WAAO,6BAA6B,OAAO;AAAA,EAC7C,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AAQO,SAAS,6BAA6B;AAC3C,QAAM,uBAAuB,oBAAI,IAAqB;AAEtD,SAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOL,mBAAmB,CAAC,QAA+B;AACjD,UAAI,CAAC,KAAK;AACR,eAAO;AAAA,MACT;AAEA,YAAM,WAAW,OAAO,QAAQ,WAAW,MAAM,IAAI;AACrD,UAAI,MAAM,qBAAqB,IAAI,QAAQ;AAC3C,UAAI,QAAQ,QAAW;AACrB,cAAM,wBAAwB,KAAK,OAAK,SAAS,SAAS,CAAC,CAAC;AAC5D,6BAAqB,IAAI,UAAU,GAAG;AAAA,MACxC;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AASO,SAAS,gCAAgC,QAAyB;AACvE,SAAO,OAAO,WAAW,OAAO,KAAK,OAAO,WAAW,UAAU;AACnE;AASO,SAAS,+BAA+B,QAAyB;AACtE,SAAO,OAAO,WAAW,OAAO,KAAK,OAAO,WAAW,UAAU;AACnE;AASO,SAAS,2BAA2B,QAAyB;AAClE,SAAO,OAAO,WAAW,OAAO,KAAK,OAAO,WAAW,UAAU;AACnE;AASO,SAAS,0BAA0B,QAAyB;AACjE,SAAO,OAAO,WAAW,OAAO,KAAK,OAAO,WAAW,UAAU;AACnE;AAUA,eAAsB,gBACpB,gBACA,SAAuB,WAAW,OAAO,QACxB;AACjB,QAAM,OAAO,IAAI,YAAY,EAAE,OAAO,cAAc;AACpD,QAAM,SAAS,MAAM,OAAO,OAAO,SAAS,IAAI;AAChD,QAAM,eAAe,OAAO,aAAa,GAAG,IAAI,WAAW,MAAM,CAAC;AAElE,SAAO,eAAe,YAAY,EAAE,QAAQ,QAAQ,GAAG,EAAE,QAAQ,QAAQ,GAAG,EAAE,UAAU,GAAG,CAAC;AAC9F;AAUO,IAAM,wBAAwB,CAAC,YAAoB,iBAAiC;AACzF,SAAO,GAAG,UAAU,IAAI,YAAY;AACtC;", "names": []}