{"version": 3, "sources": ["../src/authorization.ts"], "sourcesContent": ["import type {\n  ActClaim,\n  CheckAuthorizationWithCustomPermissions,\n  GetToken,\n  JwtPayload,\n  OrganizationCustomPermissionKey,\n  OrganizationCustomRoleKey,\n  PendingSessionOptions,\n  ReverificationConfig,\n  SessionStatusClaim,\n  SessionVerificationLevel,\n  SessionVerificationTypes,\n  SignOut,\n  UseAuthReturn,\n} from '@clerk/types';\n\ntype TypesToConfig = Record<SessionVerificationTypes, Exclude<ReverificationConfig, SessionVerificationTypes>>;\ntype AuthorizationOptions = {\n  userId: string | null | undefined;\n  orgId: string | null | undefined;\n  orgRole: string | null | undefined;\n  orgPermissions: string[] | null | undefined;\n  factorVerificationAge: [number, number] | null;\n  features: string | null | undefined;\n  plans: string | null | undefined;\n};\n\ntype CheckOrgAuthorization = (\n  params: { role?: OrganizationCustomRoleKey; permission?: OrganizationCustomPermissionKey },\n  options: Pick<AuthorizationOptions, 'orgId' | 'orgRole' | 'orgPermissions'>,\n) => boolean | null;\n\ntype CheckBillingAuthorization = (\n  params: { feature?: string; plan?: string },\n  options: Pick<AuthorizationOptions, 'plans' | 'features'>,\n) => boolean | null;\n\ntype CheckReverificationAuthorization = (\n  params: {\n    reverification?: ReverificationConfig;\n  },\n  { factorVerificationAge }: AuthorizationOptions,\n) => boolean | null;\n\nconst TYPES_TO_OBJECTS: TypesToConfig = {\n  strict_mfa: {\n    afterMinutes: 10,\n    level: 'multi_factor',\n  },\n  strict: {\n    afterMinutes: 10,\n    level: 'second_factor',\n  },\n  moderate: {\n    afterMinutes: 60,\n    level: 'second_factor',\n  },\n  lax: {\n    afterMinutes: 1_440,\n    level: 'second_factor',\n  },\n};\n\nconst ALLOWED_LEVELS = new Set<SessionVerificationLevel>(['first_factor', 'second_factor', 'multi_factor']);\n\nconst ALLOWED_TYPES = new Set<SessionVerificationTypes>(['strict_mfa', 'strict', 'moderate', 'lax']);\n\n// Helper functions\nconst isValidMaxAge = (maxAge: any) => typeof maxAge === 'number' && maxAge > 0;\nconst isValidLevel = (level: any) => ALLOWED_LEVELS.has(level);\nconst isValidVerificationType = (type: any) => ALLOWED_TYPES.has(type);\n\nconst prefixWithOrg = (value: string) => value.replace(/^(org:)*/, 'org:');\n\n/**\n * Checks if a user has the required organization-level authorization.\n * Verifies if the user has the specified role or permission within their organization.\n * @returns null, if unable to determine due to missing data or unspecified role/permission.\n */\nconst checkOrgAuthorization: CheckOrgAuthorization = (params, options) => {\n  const { orgId, orgRole, orgPermissions } = options;\n  if (!params.role && !params.permission) {\n    return null;\n  }\n\n  if (!orgId || !orgRole || !orgPermissions) {\n    return null;\n  }\n\n  if (params.permission) {\n    return orgPermissions.includes(prefixWithOrg(params.permission));\n  }\n\n  if (params.role) {\n    return prefixWithOrg(orgRole) === prefixWithOrg(params.role);\n  }\n  return null;\n};\n\nconst checkForFeatureOrPlan = (claim: string, featureOrPlan: string) => {\n  const { org: orgFeatures, user: userFeatures } = splitByScope(claim);\n  const [scope, _id] = featureOrPlan.split(':');\n  const id = _id || scope;\n\n  if (scope === 'org') {\n    return orgFeatures.includes(id);\n  } else if (scope === 'user') {\n    return userFeatures.includes(id);\n  } else {\n    // Since org scoped features will not exist if there is not an active org, merging is safe.\n    return [...orgFeatures, ...userFeatures].includes(id);\n  }\n};\n\nconst checkBillingAuthorization: CheckBillingAuthorization = (params, options) => {\n  const { features, plans } = options;\n\n  if (params.feature && features) {\n    return checkForFeatureOrPlan(features, params.feature);\n  }\n\n  if (params.plan && plans) {\n    return checkForFeatureOrPlan(plans, params.plan);\n  }\n  return null;\n};\n\nconst splitByScope = (fea: string | null | undefined) => {\n  const features = fea ? fea.split(',').map(f => f.trim()) : [];\n\n  // TODO: make this more efficient\n  return {\n    org: features.filter(f => f.split(':')[0].includes('o')).map(f => f.split(':')[1]),\n    user: features.filter(f => f.split(':')[0].includes('u')).map(f => f.split(':')[1]),\n  };\n};\n\nconst validateReverificationConfig = (config: ReverificationConfig | undefined | null) => {\n  if (!config) {\n    return false;\n  }\n\n  const convertConfigToObject = (config: ReverificationConfig) => {\n    if (typeof config === 'string') {\n      return TYPES_TO_OBJECTS[config];\n    }\n    return config;\n  };\n\n  const isValidStringValue = typeof config === 'string' && isValidVerificationType(config);\n  const isValidObjectValue =\n    typeof config === 'object' && isValidLevel(config.level) && isValidMaxAge(config.afterMinutes);\n\n  if (isValidStringValue || isValidObjectValue) {\n    return convertConfigToObject.bind(null, config);\n  }\n\n  return false;\n};\n\n/**\n * Evaluates if the user meets re-verification authentication requirements.\n * Compares the user's factor verification ages against the specified maxAge.\n * Handles different verification levels (first factor, second factor, multi-factor).\n * @returns null, if requirements or verification data are missing.\n */\nconst checkReverificationAuthorization: CheckReverificationAuthorization = (params, { factorVerificationAge }) => {\n  if (!params.reverification || !factorVerificationAge) {\n    return null;\n  }\n\n  const isValidReverification = validateReverificationConfig(params.reverification);\n  if (!isValidReverification) {\n    return null;\n  }\n\n  const { level, afterMinutes } = isValidReverification();\n  const [factor1Age, factor2Age] = factorVerificationAge;\n\n  // -1 indicates the factor group (1fa,2fa) is not enabled\n  // -1 for 1fa is not a valid scenario, but we need to make sure we handle it properly\n  const isValidFactor1 = factor1Age !== -1 ? afterMinutes > factor1Age : null;\n  const isValidFactor2 = factor2Age !== -1 ? afterMinutes > factor2Age : null;\n\n  switch (level) {\n    case 'first_factor':\n      return isValidFactor1;\n    case 'second_factor':\n      return factor2Age !== -1 ? isValidFactor2 : isValidFactor1;\n    case 'multi_factor':\n      return factor2Age === -1 ? isValidFactor1 : isValidFactor1 && isValidFactor2;\n  }\n};\n\n/**\n * Creates a function for comprehensive user authorization checks.\n * Combines organization-level and reverification authentication checks.\n * The returned function authorizes if both checks pass, or if at least one passes\n * when the other is indeterminate. Fails if userId is missing.\n */\nconst createCheckAuthorization = (options: AuthorizationOptions): CheckAuthorizationWithCustomPermissions => {\n  return (params): boolean => {\n    if (!options.userId) {\n      return false;\n    }\n\n    const billingAuthorization = checkBillingAuthorization(params, options);\n    const orgAuthorization = checkOrgAuthorization(params, options);\n    const reverificationAuthorization = checkReverificationAuthorization(params, options);\n\n    if ([billingAuthorization || orgAuthorization, reverificationAuthorization].some(a => a === null)) {\n      return [billingAuthorization || orgAuthorization, reverificationAuthorization].some(a => a === true);\n    }\n\n    return [billingAuthorization || orgAuthorization, reverificationAuthorization].every(a => a === true);\n  };\n};\n\ntype AuthStateOptions = {\n  authObject: {\n    userId?: string | null;\n    sessionId?: string | null;\n    sessionStatus?: SessionStatusClaim | null;\n    sessionClaims?: JwtPayload | null;\n    actor?: ActClaim | null;\n    orgId?: string | null;\n    orgRole?: OrganizationCustomRoleKey | null;\n    orgSlug?: string | null;\n    orgPermissions?: OrganizationCustomPermissionKey[] | null;\n    getToken: GetToken;\n    signOut: SignOut;\n    has: (params: Parameters<CheckAuthorizationWithCustomPermissions>[0]) => boolean;\n  };\n  options: PendingSessionOptions;\n};\n\n/**\n * Shared utility function that centralizes auth state resolution logic,\n * preventing duplication across different packages.\n * @internal\n */\nconst resolveAuthState = ({\n  authObject: {\n    sessionId,\n    sessionStatus,\n    userId,\n    actor,\n    orgId,\n    orgRole,\n    orgSlug,\n    signOut,\n    getToken,\n    has,\n    sessionClaims,\n  },\n  options: { treatPendingAsSignedOut = true },\n}: AuthStateOptions): UseAuthReturn | undefined => {\n  if (sessionId === undefined && userId === undefined) {\n    return {\n      isLoaded: false,\n      isSignedIn: undefined,\n      sessionId,\n      sessionClaims: undefined,\n      userId,\n      actor: undefined,\n      orgId: undefined,\n      orgRole: undefined,\n      orgSlug: undefined,\n      has: undefined,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (sessionId === null && userId === null) {\n    return {\n      isLoaded: true,\n      isSignedIn: false,\n      sessionId,\n      userId,\n      sessionClaims: null,\n      actor: null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has: () => false,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (treatPendingAsSignedOut && sessionStatus === 'pending') {\n    return {\n      isLoaded: true,\n      isSignedIn: false,\n      sessionId: null,\n      userId: null,\n      sessionClaims: null,\n      actor: null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has: () => false,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (!!sessionId && !!sessionClaims && !!userId && !!orgId && !!orgRole) {\n    return {\n      isLoaded: true,\n      isSignedIn: true,\n      sessionId,\n      sessionClaims,\n      userId,\n      actor: actor || null,\n      orgId,\n      orgRole,\n      orgSlug: orgSlug || null,\n      has,\n      signOut,\n      getToken,\n    } as const;\n  }\n\n  if (!!sessionId && !!sessionClaims && !!userId && !orgId) {\n    return {\n      isLoaded: true,\n      isSignedIn: true,\n      sessionId,\n      sessionClaims,\n      userId,\n      actor: actor || null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has,\n      signOut,\n      getToken,\n    } as const;\n  }\n};\n\nexport { createCheckAuthorization, validateReverificationConfig, resolveAuthState, splitByScope };\n"], "mappings": ";AA4CA,IAAM,mBAAkC;AAAA,EACtC,YAAY;AAAA,IACV,cAAc;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,cAAc;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,cAAc;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,KAAK;AAAA,IACH,cAAc;AAAA,IACd,OAAO;AAAA,EACT;AACF;AAEA,IAAM,iBAAiB,oBAAI,IAA8B,CAAC,gBAAgB,iBAAiB,cAAc,CAAC;AAE1G,IAAM,gBAAgB,oBAAI,IAA8B,CAAC,cAAc,UAAU,YAAY,KAAK,CAAC;AAGnG,IAAM,gBAAgB,CAAC,WAAgB,OAAO,WAAW,YAAY,SAAS;AAC9E,IAAM,eAAe,CAAC,UAAe,eAAe,IAAI,KAAK;AAC7D,IAAM,0BAA0B,CAAC,SAAc,cAAc,IAAI,IAAI;AAErE,IAAM,gBAAgB,CAAC,UAAkB,MAAM,QAAQ,YAAY,MAAM;AAOzE,IAAM,wBAA+C,CAAC,QAAQ,YAAY;AACxE,QAAM,EAAE,OAAO,SAAS,eAAe,IAAI;AAC3C,MAAI,CAAC,OAAO,QAAQ,CAAC,OAAO,YAAY;AACtC,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,SAAS,CAAC,WAAW,CAAC,gBAAgB;AACzC,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,YAAY;AACrB,WAAO,eAAe,SAAS,cAAc,OAAO,UAAU,CAAC;AAAA,EACjE;AAEA,MAAI,OAAO,MAAM;AACf,WAAO,cAAc,OAAO,MAAM,cAAc,OAAO,IAAI;AAAA,EAC7D;AACA,SAAO;AACT;AAEA,IAAM,wBAAwB,CAAC,OAAe,kBAA0B;AACtE,QAAM,EAAE,KAAK,aAAa,MAAM,aAAa,IAAI,aAAa,KAAK;AACnE,QAAM,CAAC,OAAO,GAAG,IAAI,cAAc,MAAM,GAAG;AAC5C,QAAM,KAAK,OAAO;AAElB,MAAI,UAAU,OAAO;AACnB,WAAO,YAAY,SAAS,EAAE;AAAA,EAChC,WAAW,UAAU,QAAQ;AAC3B,WAAO,aAAa,SAAS,EAAE;AAAA,EACjC,OAAO;AAEL,WAAO,CAAC,GAAG,aAAa,GAAG,YAAY,EAAE,SAAS,EAAE;AAAA,EACtD;AACF;AAEA,IAAM,4BAAuD,CAAC,QAAQ,YAAY;AAChF,QAAM,EAAE,UAAU,MAAM,IAAI;AAE5B,MAAI,OAAO,WAAW,UAAU;AAC9B,WAAO,sBAAsB,UAAU,OAAO,OAAO;AAAA,EACvD;AAEA,MAAI,OAAO,QAAQ,OAAO;AACxB,WAAO,sBAAsB,OAAO,OAAO,IAAI;AAAA,EACjD;AACA,SAAO;AACT;AAEA,IAAM,eAAe,CAAC,QAAmC;AACvD,QAAM,WAAW,MAAM,IAAI,MAAM,GAAG,EAAE,IAAI,OAAK,EAAE,KAAK,CAAC,IAAI,CAAC;AAG5D,SAAO;AAAA,IACL,KAAK,SAAS,OAAO,OAAK,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,IAAI,OAAK,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC;AAAA,IACjF,MAAM,SAAS,OAAO,OAAK,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,IAAI,OAAK,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC;AAAA,EACpF;AACF;AAEA,IAAM,+BAA+B,CAAC,WAAoD;AACxF,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AAEA,QAAM,wBAAwB,CAACA,YAAiC;AAC9D,QAAI,OAAOA,YAAW,UAAU;AAC9B,aAAO,iBAAiBA,OAAM;AAAA,IAChC;AACA,WAAOA;AAAA,EACT;AAEA,QAAM,qBAAqB,OAAO,WAAW,YAAY,wBAAwB,MAAM;AACvF,QAAM,qBACJ,OAAO,WAAW,YAAY,aAAa,OAAO,KAAK,KAAK,cAAc,OAAO,YAAY;AAE/F,MAAI,sBAAsB,oBAAoB;AAC5C,WAAO,sBAAsB,KAAK,MAAM,MAAM;AAAA,EAChD;AAEA,SAAO;AACT;AAQA,IAAM,mCAAqE,CAAC,QAAQ,EAAE,sBAAsB,MAAM;AAChH,MAAI,CAAC,OAAO,kBAAkB,CAAC,uBAAuB;AACpD,WAAO;AAAA,EACT;AAEA,QAAM,wBAAwB,6BAA6B,OAAO,cAAc;AAChF,MAAI,CAAC,uBAAuB;AAC1B,WAAO;AAAA,EACT;AAEA,QAAM,EAAE,OAAO,aAAa,IAAI,sBAAsB;AACtD,QAAM,CAAC,YAAY,UAAU,IAAI;AAIjC,QAAM,iBAAiB,eAAe,KAAK,eAAe,aAAa;AACvE,QAAM,iBAAiB,eAAe,KAAK,eAAe,aAAa;AAEvE,UAAQ,OAAO;AAAA,IACb,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO,eAAe,KAAK,iBAAiB;AAAA,IAC9C,KAAK;AACH,aAAO,eAAe,KAAK,iBAAiB,kBAAkB;AAAA,EAClE;AACF;AAQA,IAAM,2BAA2B,CAAC,YAA2E;AAC3G,SAAO,CAAC,WAAoB;AAC1B,QAAI,CAAC,QAAQ,QAAQ;AACnB,aAAO;AAAA,IACT;AAEA,UAAM,uBAAuB,0BAA0B,QAAQ,OAAO;AACtE,UAAM,mBAAmB,sBAAsB,QAAQ,OAAO;AAC9D,UAAM,8BAA8B,iCAAiC,QAAQ,OAAO;AAEpF,QAAI,CAAC,wBAAwB,kBAAkB,2BAA2B,EAAE,KAAK,OAAK,MAAM,IAAI,GAAG;AACjG,aAAO,CAAC,wBAAwB,kBAAkB,2BAA2B,EAAE,KAAK,OAAK,MAAM,IAAI;AAAA,IACrG;AAEA,WAAO,CAAC,wBAAwB,kBAAkB,2BAA2B,EAAE,MAAM,OAAK,MAAM,IAAI;AAAA,EACtG;AACF;AAyBA,IAAM,mBAAmB,CAAC;AAAA,EACxB,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,SAAS,EAAE,0BAA0B,KAAK;AAC5C,MAAmD;AACjD,MAAI,cAAc,UAAa,WAAW,QAAW;AACnD,WAAO;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ;AAAA,MACA,eAAe;AAAA,MACf;AAAA,MACA,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,KAAK;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,MAAI,cAAc,QAAQ,WAAW,MAAM;AACzC,WAAO;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA,eAAe;AAAA,MACf,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,KAAK,MAAM;AAAA,MACX;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,MAAI,2BAA2B,kBAAkB,WAAW;AAC1D,WAAO;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,eAAe;AAAA,MACf,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,KAAK,MAAM;AAAA,MACX;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,MAAI,CAAC,CAAC,aAAa,CAAC,CAAC,iBAAiB,CAAC,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,CAAC,SAAS;AACtE,WAAO;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO,SAAS;AAAA,MAChB;AAAA,MACA;AAAA,MACA,SAAS,WAAW;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,MAAI,CAAC,CAAC,aAAa,CAAC,CAAC,iBAAiB,CAAC,CAAC,UAAU,CAAC,OAAO;AACxD,WAAO;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO,SAAS;AAAA,MAChB,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;", "names": ["config"]}