{"version": 3, "sources": ["../src/logger.ts"], "sourcesContent": ["const loggedMessages: Set<string> = new Set();\n\nexport const logger = {\n  /**\n   * A custom logger that ensures messages are logged only once.\n   * Reduces noise and duplicated messages when logs are in a hot codepath.\n   */\n  warnOnce: (msg: string) => {\n    if (loggedMessages.has(msg)) {\n      return;\n    }\n\n    loggedMessages.add(msg);\n    console.warn(msg);\n  },\n  logOnce: (msg: string) => {\n    if (loggedMessages.has(msg)) {\n      return;\n    }\n\n    console.log(msg);\n    loggedMessages.add(msg);\n  },\n};\n"], "mappings": ";AAAA,IAAM,iBAA8B,oBAAI,IAAI;AAErC,IAAM,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,UAAU,CAAC,QAAgB;AACzB,QAAI,eAAe,IAAI,GAAG,GAAG;AAC3B;AAAA,IACF;AAEA,mBAAe,IAAI,GAAG;AACtB,YAAQ,KAAK,GAAG;AAAA,EAClB;AAAA,EACA,SAAS,CAAC,QAAgB;AACxB,QAAI,eAAe,IAAI,GAAG,GAAG;AAC3B;AAAA,IACF;AAEA,YAAQ,IAAI,GAAG;AACf,mBAAe,IAAI,GAAG;AAAA,EACxB;AACF;", "names": []}