import { PublishableKey } from '@clerk/types';

/**
 * Configuration options for parsing publishable keys.
 */
type ParsePublishableKeyOptions = {
    /** Whether to throw an error if parsing fails */
    fatal?: boolean;
    /** Custom domain to use for satellite instances */
    domain?: string;
    /** Proxy URL to use instead of the decoded frontend API */
    proxyUrl?: string;
    /** Whether this is a satellite instance */
    isSatellite?: boolean;
};
/**
 * Converts a frontend API URL into a base64-encoded publishable key.
 *
 * @param frontendApi - The frontend API URL (e.g., 'clerk.example.com').
 * @returns A base64-encoded publishable key with appropriate prefix (pk_live_ or pk_test_).
 */
declare function buildPublishableKey(frontendApi: string): string;
declare function parsePublishableKey(key: string | undefined, options: ParsePublishableKeyOptions & {
    fatal: true;
}): PublishableKey;
declare function parsePublishableKey(key: string | undefined, options?: ParsePublishableKeyOptions): PublishableKey | null;
/**
 * Checks if the provided key is a valid publishable key.
 *
 * @param key - The key to be checked. Defaults to an empty string if not provided.
 * @returns `true` if 'key' is a valid publishable key, `false` otherwise.
 */
declare function isPublishableKey(key?: string): boolean;
/**
 * Creates a memoized cache for checking if URLs are development or staging environments.
 * Uses a Map to cache results for better performance on repeated checks.
 *
 * @returns An object with an isDevOrStagingUrl method that checks if a URL is dev/staging.
 */
declare function createDevOrStagingUrlCache(): {
    /**
     * Checks if a URL is a development or staging environment.
     *
     * @param url - The URL to check (string or URL object).
     * @returns `true` if the URL is a development or staging environment, `false` otherwise.
     */
    isDevOrStagingUrl: (url: string | URL) => boolean;
};
/**
 * Checks if a publishable key is for a development environment.
 * Supports both legacy format (test_) and new format (pk_test_).
 *
 * @param apiKey - The API key to check.
 * @returns `true` if the key is for development, `false` otherwise.
 */
declare function isDevelopmentFromPublishableKey(apiKey: string): boolean;
/**
 * Checks if a publishable key is for a production environment.
 * Supports both legacy format (live_) and new format (pk_live_).
 *
 * @param apiKey - The API key to check.
 * @returns `true` if the key is for production, `false` otherwise.
 */
declare function isProductionFromPublishableKey(apiKey: string): boolean;
/**
 * Checks if a secret key is for a development environment.
 * Supports both legacy format (test_) and new format (sk_test_).
 *
 * @param apiKey - The secret key to check.
 * @returns `true` if the key is for development, `false` otherwise.
 */
declare function isDevelopmentFromSecretKey(apiKey: string): boolean;
/**
 * Checks if a secret key is for a production environment.
 * Supports both legacy format (live_) and new format (sk_live_).
 *
 * @param apiKey - The secret key to check.
 * @returns `true` if the key is for production, `false` otherwise.
 */
declare function isProductionFromSecretKey(apiKey: string): boolean;
/**
 * Generates a unique cookie suffix based on the publishable key using SHA-1 hashing.
 * The suffix is base64-encoded and URL-safe (+ and / characters are replaced).
 *
 * @param publishableKey - The publishable key to generate suffix from.
 * @param subtle - The SubtleCrypto interface to use for hashing (defaults to globalThis.crypto.subtle).
 * @returns A promise that resolves to an 8-character URL-safe base64 string.
 */
declare function getCookieSuffix(publishableKey: string, subtle?: SubtleCrypto): Promise<string>;
/**
 * Creates a suffixed cookie name by appending the cookie suffix to the base name.
 * Used to create unique cookie names based on the publishable key.
 *
 * @param cookieName - The base cookie name.
 * @param cookieSuffix - The suffix to append (typically generated by getCookieSuffix).
 * @returns The suffixed cookie name in format: `${cookieName}_${cookieSuffix}`.
 */
declare const getSuffixedCookieName: (cookieName: string, cookieSuffix: string) => string;

export { buildPublishableKey, createDevOrStagingUrlCache, getCookieSuffix, getSuffixedCookieName, isDevelopmentFromPublishableKey, isDevelopmentFromSecretKey, isProductionFromPublishableKey, isProductionFromSecretKey, isPublishableKey, parsePublishableKey };
