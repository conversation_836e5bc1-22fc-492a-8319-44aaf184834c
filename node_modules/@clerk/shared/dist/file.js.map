{"version": 3, "sources": ["../src/file.ts"], "sourcesContent": ["/**\n * Read an expected JSON type File.\n *\n * Probably paired with:\n *  <input type='file' accept='application/JSON' ... />\n */\nexport function readJSONFile(file: File): Promise<unknown> {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.addEventListener('load', function () {\n      const result = JSON.parse(reader.result as string);\n      resolve(result);\n    });\n\n    reader.addEventListener('error', reject);\n    reader.readAsText(file);\n  });\n}\n\nconst MimeTypeToExtensionMap = Object.freeze({\n  'image/png': 'png',\n  'image/jpeg': 'jpg',\n  'image/gif': 'gif',\n  'image/webp': 'webp',\n  'image/x-icon': 'ico',\n  'image/vnd.microsoft.icon': 'ico',\n} as const);\n\nexport type SupportedMimeType = keyof typeof MimeTypeToExtensionMap;\n\nexport const extension = (mimeType: SupportedMimeType): string => {\n  return MimeTypeToExtensionMap[mimeType];\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMO,SAAS,aAAa,MAA8B;AACzD,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAM,SAAS,IAAI,WAAW;AAC9B,WAAO,iBAAiB,QAAQ,WAAY;AAC1C,YAAM,SAAS,KAAK,MAAM,OAAO,MAAgB;AACjD,cAAQ,MAAM;AAAA,IAChB,CAAC;AAED,WAAO,iBAAiB,SAAS,MAAM;AACvC,WAAO,WAAW,IAAI;AAAA,EACxB,CAAC;AACH;AAEA,IAAM,yBAAyB,OAAO,OAAO;AAAA,EAC3C,aAAa;AAAA,EACb,cAAc;AAAA,EACd,aAAa;AAAA,EACb,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,4BAA4B;AAC9B,CAAU;AAIH,IAAM,YAAY,CAAC,aAAwC;AAChE,SAAO,uBAAuB,QAAQ;AACxC;", "names": []}