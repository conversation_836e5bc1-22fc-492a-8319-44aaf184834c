import {
  Clerk<PERSON><PERSON><PERSON>po<PERSON><PERSON><PERSON>r,
  <PERSON><PERSON><PERSON><PERSON>Erro<PERSON>,
  ClerkWebAuthnError,
  EmailLinkError,
  EmailLinkErrorCode,
  EmailLinkErrorCodeStatus,
  buildErrorThrower,
  errorToJSON,
  is4xxError,
  isCaptchaError,
  isClerkAPIResponseError,
  isClerkRuntimeError,
  isEmailLinkError,
  isKnownError,
  isMetamaskError,
  isNetworkError,
  isPasswordPwnedError,
  isReverificationCancelledError,
  isUnauthorizedError,
  isUserLockedError,
  parseError,
  parseErrors
} from "./chunk-35WGBVWP.mjs";
import "./chunk-7ELT755Q.mjs";
export {
  ClerkAPIResponseError,
  ClerkRuntimeError,
  ClerkWebAuthnError,
  EmailLinkError,
  EmailLinkErrorCode,
  EmailLinkErrorCodeStatus,
  buildErrorThrower,
  errorToJSON,
  is4xxError,
  isCaptchaError,
  is<PERSON><PERSON>kAPIResponseError,
  isClerkRuntimeError,
  isEmailLinkError,
  isKnownError,
  isMetamaskError,
  isNetworkError,
  isPasswordPwnedError,
  isReverificationCancelledError,
  isUnauthorizedError,
  isUserLockedError,
  parseError,
  parseErrors
};
//# sourceMappingURL=error.mjs.map