/**
 * Retrieves an environment variable across runtime environments.
 *
 * @param name - The environment variable name to retrieve.
 * @param context - Optional context object that may contain environment values.
 * @returns The environment variable value or empty string if not found.
 */
declare const getEnvVariable: (name: string, context?: Record<string, any>) => string;

export { getEnvVariable };
