{"version": 3, "sources": ["../../src/utils/index.ts", "../../src/utils/noop.ts", "../../src/utils/createDeferredPromise.ts", "../../src/utils/allSettled.ts", "../../src/utils/instance.ts", "../../src/utils/runtimeEnvironment.ts", "../../src/utils/logErrorInDevMode.ts", "../../src/utils/handleValueOrFn.ts", "../../src/utils/fastDeepMerge.ts"], "sourcesContent": ["export * from './createDeferredPromise';\nexport * from './allSettled';\nexport { isStaging } from './instance';\nexport { logErrorInDevMode } from './logErrorInDevMode';\nexport { noop } from './noop';\nexport * from './runtimeEnvironment';\nexport { handleValueOrFn } from './handleValueOrFn';\nexport { fastDeepMergeAndReplace, fastDeepMergeAndKeep } from './fastDeepMerge';\n", "export const noop = (..._args: any[]): void => {\n  // do nothing.\n};\n", "import { noop } from './noop';\n\ntype Callback = (val?: any) => void;\n\n/**\n * Create a promise that can be resolved or rejected from\n * outside the Promise constructor callback\n * A ES6 compatible utility that implements `Promise.withResolvers`\n * @internal\n */\nexport const createDeferredPromise = () => {\n  let resolve: Callback = noop;\n  let reject: Callback = noop;\n  const promise = new Promise((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n  return { promise, resolve, reject };\n};\n", "/**\n * A ES6 compatible utility that implements `Promise.allSettled`\n * @internal\n */\nexport function allSettled<T>(\n  iterable: Iterable<Promise<T>>,\n): Promise<({ status: 'fulfilled'; value: T } | { status: 'rejected'; reason: any })[]> {\n  const promises = Array.from(iterable).map(p =>\n    p.then(\n      value => ({ status: 'fulfilled', value }) as const,\n      reason => ({ status: 'rejected', reason }) as const,\n    ),\n  );\n  return Promise.all(promises);\n}\n", "/**\n * Check if the frontendApi ends with a staging domain\n */\nexport function isStaging(frontendApi: string): boolean {\n  return (\n    frontendApi.endsWith('.lclstage.dev') ||\n    frontendApi.endsWith('.stgstage.dev') ||\n    frontendApi.endsWith('.clerkstage.dev') ||\n    frontendApi.endsWith('.accountsstage.dev')\n  );\n}\n", "export const isDevelopmentEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'development';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n\n  return false;\n};\n\nexport const isTestEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'test';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n\nexport const isProductionEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'production';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n", "import { isDevelopmentEnvironment } from './runtimeEnvironment';\n\nexport const logErrorInDevMode = (message: string) => {\n  if (isDevelopmentEnvironment()) {\n    console.error(`Clerk: ${message}`);\n  }\n};\n", "type VOrFnReturnsV<T> = T | undefined | ((v: URL) => T);\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL): T | undefined;\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL, defaultValue: T): T;\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL, defaultValue?: unknown): unknown {\n  if (typeof value === 'function') {\n    return (value as (v: URL) => T)(url);\n  }\n\n  if (typeof value !== 'undefined') {\n    return value;\n  }\n\n  if (typeof defaultValue !== 'undefined') {\n    return defaultValue;\n  }\n\n  return undefined;\n}\n", "/**\n * Merges 2 objects without creating new object references\n * The merged props will appear on the `target` object\n * If `target` already has a value for a given key it will not be overwritten\n */\nexport const fastDeepMergeAndReplace = (\n  source: Record<any, any> | undefined | null,\n  target: Record<any, any> | undefined | null,\n) => {\n  if (!source || !target) {\n    return;\n  }\n\n  for (const key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key) && source[key] !== null && typeof source[key] === `object`) {\n      if (target[key] === undefined) {\n        target[key] = new (Object.getPrototypeOf(source[key]).constructor)();\n      }\n      fastDeepMergeAndReplace(source[key], target[key]);\n    } else if (Object.prototype.hasOwnProperty.call(source, key)) {\n      target[key] = source[key];\n    }\n  }\n};\n\nexport const fastDeepMergeAndKeep = (\n  source: Record<any, any> | undefined | null,\n  target: Record<any, any> | undefined | null,\n) => {\n  if (!source || !target) {\n    return;\n  }\n\n  for (const key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key) && source[key] !== null && typeof source[key] === `object`) {\n      if (target[key] === undefined) {\n        target[key] = new (Object.getPrototypeOf(source[key]).constructor)();\n      }\n      fastDeepMergeAndKeep(source[key], target[key]);\n    } else if (Object.prototype.hasOwnProperty.call(source, key) && target[key] === undefined) {\n      target[key] = source[key];\n    }\n  }\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAO,IAAM,OAAO,IAAI,UAAuB;AAE/C;;;ACQO,IAAM,wBAAwB,MAAM;AACzC,MAAI,UAAoB;AACxB,MAAI,SAAmB;AACvB,QAAM,UAAU,IAAI,QAAQ,CAAC,KAAK,QAAQ;AACxC,cAAU;AACV,aAAS;AAAA,EACX,CAAC;AACD,SAAO,EAAE,SAAS,SAAS,OAAO;AACpC;;;ACdO,SAAS,WACd,UACsF;AACtF,QAAM,WAAW,MAAM,KAAK,QAAQ,EAAE;AAAA,IAAI,OACxC,EAAE;AAAA,MACA,YAAU,EAAE,QAAQ,aAAa,MAAM;AAAA,MACvC,aAAW,EAAE,QAAQ,YAAY,OAAO;AAAA,IAC1C;AAAA,EACF;AACA,SAAO,QAAQ,IAAI,QAAQ;AAC7B;;;ACXO,SAAS,UAAU,aAA8B;AACtD,SACE,YAAY,SAAS,eAAe,KACpC,YAAY,SAAS,eAAe,KACpC,YAAY,SAAS,iBAAiB,KACtC,YAAY,SAAS,oBAAoB;AAE7C;;;ACVO,IAAM,2BAA2B,MAAe;AACrD,MAAI;AACF,WAAO,QAAQ,IAAI,aAAa;AAAA,EAElC,QAAQ;AAAA,EAAC;AAIT,SAAO;AACT;AAEO,IAAM,oBAAoB,MAAe;AAC9C,MAAI;AACF,WAAO,QAAQ,IAAI,aAAa;AAAA,EAElC,QAAQ;AAAA,EAAC;AAGT,SAAO;AACT;AAEO,IAAM,0BAA0B,MAAe;AACpD,MAAI;AACF,WAAO,QAAQ,IAAI,aAAa;AAAA,EAElC,QAAQ;AAAA,EAAC;AAGT,SAAO;AACT;;;AC3BO,IAAM,oBAAoB,CAAC,YAAoB;AACpD,MAAI,yBAAyB,GAAG;AAC9B,YAAQ,MAAM,UAAU,OAAO,EAAE;AAAA,EACnC;AACF;;;ACHO,SAAS,gBAAmB,OAAyB,KAAU,cAAiC;AACrG,MAAI,OAAO,UAAU,YAAY;AAC/B,WAAQ,MAAwB,GAAG;AAAA,EACrC;AAEA,MAAI,OAAO,UAAU,aAAa;AAChC,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,iBAAiB,aAAa;AACvC,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ACZO,IAAM,0BAA0B,CACrC,QACA,WACG;AACH,MAAI,CAAC,UAAU,CAAC,QAAQ;AACtB;AAAA,EACF;AAEA,aAAW,OAAO,QAAQ;AACxB,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,KAAK,OAAO,GAAG,MAAM,QAAQ,OAAO,OAAO,GAAG,MAAM,UAAU;AAChH,UAAI,OAAO,GAAG,MAAM,QAAW;AAC7B,eAAO,GAAG,IAAI,KAAK,OAAO,eAAe,OAAO,GAAG,CAAC,GAAE,YAAa;AAAA,MACrE;AACA,8BAAwB,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;AAAA,IAClD,WAAW,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAC5D,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AACF;AAEO,IAAM,uBAAuB,CAClC,QACA,WACG;AACH,MAAI,CAAC,UAAU,CAAC,QAAQ;AACtB;AAAA,EACF;AAEA,aAAW,OAAO,QAAQ;AACxB,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,KAAK,OAAO,GAAG,MAAM,QAAQ,OAAO,OAAO,GAAG,MAAM,UAAU;AAChH,UAAI,OAAO,GAAG,MAAM,QAAW;AAC7B,eAAO,GAAG,IAAI,KAAK,OAAO,eAAe,OAAO,GAAG,CAAC,GAAE,YAAa;AAAA,MACrE;AACA,2BAAqB,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;AAAA,IAC/C,WAAW,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,KAAK,OAAO,GAAG,MAAM,QAAW;AACzF,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AACF;", "names": []}