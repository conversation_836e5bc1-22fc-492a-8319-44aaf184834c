{"name": "@clerk/shared", "version": "3.17.0", "description": "Internal package utils used by the Clerk SDKs", "repository": {"type": "git", "url": "git+https://github.com/clerk/javascript.git", "directory": "packages/shared"}, "license": "MIT", "author": "Clerk", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "./*": {"import": {"types": "./dist/*.d.mts", "default": "./dist/*.mjs"}, "require": {"types": "./dist/*.d.ts", "default": "./dist/*.js"}}, "./react": {"import": {"types": "./dist/react/index.d.mts", "default": "./dist/react/index.mjs"}, "require": {"types": "./dist/react/index.d.ts", "default": "./dist/react/index.js"}}, "./utils": {"import": {"types": "./dist/utils/index.d.mts", "default": "./dist/utils/index.mjs"}, "require": {"types": "./dist/utils/index.d.ts", "default": "./dist/utils/index.js"}}, "./workerTimers": {"import": {"types": "./dist/workerTimers/index.d.mts", "default": "./dist/workerTimers/index.mjs"}, "require": {"types": "./dist/workerTimers/index.d.ts", "default": "./dist/workerTimers/index.js"}}, "./dom": {"import": {"types": "./dist/dom/index.d.mts", "default": "./dist/dom/index.mjs"}, "require": {"types": "./dist/dom/index.d.ts", "default": "./dist/dom/index.js"}}, "./package.json": "./package.json"}, "main": "./dist/index.js", "files": ["dist", "scripts", "authorization", "authorization-errors", "browser", "retry", "color", "cookie", "date", "deprecated", "deriveState", "dom", "error", "file", "globs", "handleValueOrFn", "isomorphicAtob", "isomorphicBtoa", "keys", "loadClerkJsScript", "loadScript", "localStorageBroadcastChannel", "poller", "proxy", "underscore", "url", "versionSelector", "react", "constants", "apiUrlFromPublishableKey", "telemetry", "logger", "webauthn", "router", "pathToRegexp", "utils", "workerTimers", "dev<PERSON><PERSON><PERSON>", "object", "o<PERSON>h", "web3", "getEnvVariable", "pathMatcher", "organization", "jwtPayloadParser", "eventBus", "netlifyCacheHandler", "clerkEvent<PERSON>us", "phoneCodeChannel"], "dependencies": {"dequal": "2.0.3", "glob-to-regexp": "0.4.1", "js-cookie": "3.0.5", "std-env": "^3.9.0", "swr": "2.3.4", "@clerk/types": "^4.72.0"}, "devDependencies": {"@stripe/react-stripe-js": "3.1.1", "@stripe/stripe-js": "5.6.0", "@types/glob-to-regexp": "0.4.4", "@types/js-cookie": "3.0.6", "cross-fetch": "^4.1.0", "esbuild": "0.25.0"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0 || ^19.0.0-0", "react-dom": "^18.0.0 || ^19.0.0 || ^19.0.0-0"}, "peerDependenciesMeta": {"react": {"optional": true}, "react-dom": {"optional": true}}, "engines": {"node": ">=18.17.0"}, "publishConfig": {"access": "public"}, "scripts": {"build": "tsup", "postbuild": "node ../../scripts/subpath-workaround.mjs shared", "build:declarations": "tsc -p tsconfig.declarations.json", "clean": "rimraf ./dist", "dev": "tsup --watch", "dev:publish": "pnpm dev -- --env.publish", "format": "node ../../scripts/format-package.mjs", "format:check": "node ../../scripts/format-package.mjs --check", "postinstall": "node ./scripts/postinstall.mjs", "lint": "eslint src", "lint:attw": "attw --pack . --profile node16", "lint:publint": "publint", "publish:local": "pnpm yalc push --replace  --sig", "test": "jest && vitest", "test:cache:clear": "jest --clearCache --useStderr", "test:ci": "jest --maxWorkers=70%", "test:coverage": "jest --collectCoverage && open coverage/lcov-report/index.html"}}