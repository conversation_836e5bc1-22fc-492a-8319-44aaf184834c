{"version": 3, "sources": ["../../../../../src/cli/lib/localDeployment/run.ts"], "sourcesContent": ["import { Context, logVerbose, logMessage } from \"../../../bundler/context.js\";\nimport {\n  LocalDeploymentKind,\n  deploymentStateDir,\n  loadUuidForAnonymousUser,\n} from \"./filePaths.js\";\nimport path from \"path\";\nimport child_process from \"child_process\";\nimport detect from \"detect-port\";\nimport { SENTRY_DSN } from \"../utils/sentry.js\";\nimport { createHash } from \"crypto\";\nimport { LocalDeploymentError } from \"./errors.js\";\n\nexport async function runLocalBackend(\n  ctx: Context,\n  args: {\n    ports: {\n      cloud: number;\n      site: number;\n    };\n    deploymentKind: LocalDeploymentKind;\n    deploymentName: string;\n    binaryPath: string;\n    instanceSecret: string;\n    isLatestVersion: boolean;\n  },\n): Promise<{\n  cleanupHandle: string;\n}> {\n  const { ports } = args;\n  const deploymentDir = deploymentStateDir(\n    args.deploymentKind,\n    args.deploymentName,\n  );\n  ctx.fs.mkdir(deploymentDir, { recursive: true });\n  const deploymentNameSha = createHash(\"sha256\")\n    .update(args.deploymentName)\n    .digest(\"hex\");\n  const commandArgs = [\n    \"--port\",\n    ports.cloud.toString(),\n    \"--site-proxy-port\",\n    ports.site.toString(),\n    \"--sentry-identifier\",\n    deploymentNameSha,\n    \"--instance-name\",\n    args.deploymentName,\n    \"--instance-secret\",\n    args.instanceSecret,\n    \"--local-storage\",\n    path.join(deploymentDir, \"convex_local_storage\"),\n    \"--beacon-tag\",\n    selfHostedEventTag(args.deploymentKind),\n    path.join(deploymentDir, \"convex_local_backend.sqlite3\"),\n  ];\n  if (args.isLatestVersion) {\n    // CLI args that were added in later versions of backend go here instead of above\n    // since the CLI may run older versions of backend (e.g. when upgrading).\n    if (args.deploymentKind === \"anonymous\") {\n      const uuid = loadUuidForAnonymousUser(ctx);\n      if (uuid !== null) {\n        commandArgs.push(\n          \"--beacon-fields\",\n          JSON.stringify({\n            override_uuid: uuid,\n          }),\n        );\n      }\n    }\n  }\n\n  // Check that binary works by running with --help\n  try {\n    const result = child_process.spawnSync(args.binaryPath, [\n      ...commandArgs,\n      \"--help\",\n    ]);\n    if (result.status === 3221225781) {\n      const message =\n        \"Local backend exited because shared libraries are missing. These may include libraries installed via 'Microsoft Visual C++ Redistributable for Visual Studio.'\";\n      return ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: message,\n        errForSentry: new LocalDeploymentError(\n          \"Local backend exited with code 3221225781\",\n        ),\n      });\n    } else if (result.status !== 0) {\n      const message = `Failed to run backend binary, exit code ${result.status}, error: ${result.stderr === null ? \"null\" : result.stderr.toString()}`;\n      return ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: message,\n        errForSentry: new LocalDeploymentError(message),\n      });\n    }\n  } catch (e) {\n    const message = `Failed to run backend binary: ${(e as any).toString()}`;\n    return ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: message,\n      errForSentry: new LocalDeploymentError(message),\n    });\n  }\n  const commandStr = `${args.binaryPath} ${commandArgs.join(\" \")}`;\n  logVerbose(ctx, `Starting local backend: \\`${commandStr}\\``);\n  const p = child_process\n    .spawn(args.binaryPath, commandArgs, {\n      stdio: \"ignore\",\n      env: {\n        ...process.env,\n        SENTRY_DSN: SENTRY_DSN,\n      },\n    })\n    .on(\"exit\", (code) => {\n      const why = code === null ? \"from signal\" : `with code ${code}`;\n      logVerbose(\n        ctx,\n        `Local backend exited ${why}, full command \\`${commandStr}\\``,\n      );\n    });\n  const cleanupHandle = ctx.registerCleanup(async () => {\n    logVerbose(ctx, `Stopping local backend on port ${ports.cloud}`);\n    p.kill(\"SIGTERM\");\n  });\n\n  await ensureBackendRunning(ctx, {\n    cloudPort: ports.cloud,\n    deploymentName: args.deploymentName,\n    maxTimeSecs: 10,\n  });\n\n  return {\n    cleanupHandle,\n  };\n}\n\n/** Crash if correct local backend is not currently listening on the expected port. */\nexport async function assertLocalBackendRunning(\n  ctx: Context,\n  args: {\n    url: string;\n    deploymentName: string;\n  },\n): Promise<void> {\n  logVerbose(ctx, `Checking local backend at ${args.url} is running`);\n  try {\n    const resp = await fetch(`${args.url}/instance_name`);\n    if (resp.status === 200) {\n      const text = await resp.text();\n      if (text !== args.deploymentName) {\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"fatal\",\n          printedMessage: `A different local backend ${text} is running at ${args.url}`,\n        });\n      } else {\n        return;\n      }\n    } else {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage: `Error response code received from local backend ${resp.status} ${resp.statusText}`,\n      });\n    }\n  } catch {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `Local backend isn't running. (it's not listening at ${args.url})\\nRun \\`npx convex dev\\` in another terminal first.`,\n    });\n  }\n}\n\n/** Wait for up to maxTimeSecs for the correct local backend to be running on the expected port. */\nexport async function ensureBackendRunning(\n  ctx: Context,\n  args: {\n    cloudPort: number;\n    deploymentName: string;\n    maxTimeSecs: number;\n  },\n): Promise<void> {\n  logVerbose(\n    ctx,\n    `Ensuring backend running on port ${args.cloudPort} is running`,\n  );\n  const deploymentUrl = localDeploymentUrl(args.cloudPort);\n  let timeElapsedSecs = 0;\n  let hasShownWaiting = false;\n  while (timeElapsedSecs <= args.maxTimeSecs) {\n    if (!hasShownWaiting && timeElapsedSecs > 2) {\n      logMessage(ctx, \"waiting for local backend to start...\");\n      hasShownWaiting = true;\n    }\n    try {\n      const resp = await fetch(`${deploymentUrl}/instance_name`);\n      if (resp.status === 200) {\n        const text = await resp.text();\n        if (text !== args.deploymentName) {\n          return await ctx.crash({\n            exitCode: 1,\n            errorType: \"fatal\",\n            printedMessage: `A different local backend ${text} is running on selected port ${args.cloudPort}`,\n          });\n        } else {\n          // The backend is running!\n          return;\n        }\n      } else {\n        await new Promise((resolve) => setTimeout(resolve, 500));\n        timeElapsedSecs += 0.5;\n      }\n    } catch {\n      await new Promise((resolve) => setTimeout(resolve, 500));\n      timeElapsedSecs += 0.5;\n    }\n  }\n  const message = `Local backend did not start on port ${args.cloudPort} within ${args.maxTimeSecs} seconds.`;\n  return await ctx.crash({\n    exitCode: 1,\n    errorType: \"fatal\",\n    printedMessage: message,\n    errForSentry: new LocalDeploymentError(message),\n  });\n}\n\nexport async function ensureBackendStopped(\n  ctx: Context,\n  args: {\n    ports: {\n      cloud: number;\n      site?: number;\n    };\n    maxTimeSecs: number;\n    deploymentName: string;\n    // Whether to allow a deployment with a different name to run on this port\n    allowOtherDeployments: boolean;\n  },\n) {\n  logVerbose(\n    ctx,\n    `Ensuring backend running on port ${args.ports.cloud} is stopped`,\n  );\n  let timeElapsedSecs = 0;\n  while (timeElapsedSecs < args.maxTimeSecs) {\n    const cloudPort = await detect(args.ports.cloud);\n    const sitePort =\n      args.ports.site === undefined ? undefined : await detect(args.ports.site);\n    // Both ports are free\n    if (cloudPort === args.ports.cloud && sitePort === args.ports.site) {\n      return;\n    }\n    try {\n      const instanceNameResp = await fetch(\n        `${localDeploymentUrl(args.ports.cloud)}/instance_name`,\n      );\n      if (instanceNameResp.ok) {\n        const instanceName = await instanceNameResp.text();\n        if (instanceName !== args.deploymentName) {\n          if (args.allowOtherDeployments) {\n            return;\n          }\n          return await ctx.crash({\n            exitCode: 1,\n            errorType: \"fatal\",\n            printedMessage: `A different local backend ${instanceName} is running on selected port ${args.ports.cloud}`,\n          });\n        }\n      }\n    } catch (error: any) {\n      logVerbose(ctx, `Error checking if backend is running: ${error.message}`);\n      // Backend is probably not running\n      continue;\n    }\n    await new Promise((resolve) => setTimeout(resolve, 500));\n    timeElapsedSecs += 0.5;\n  }\n  return ctx.crash({\n    exitCode: 1,\n    errorType: \"fatal\",\n    printedMessage: `A local backend is still running on port ${args.ports.cloud}. Please stop it and run this command again.`,\n  });\n}\n\nexport function localDeploymentUrl(cloudPort: number): string {\n  return `http://127.0.0.1:${cloudPort}`;\n}\n\nexport function selfHostedEventTag(\n  deploymentKind: LocalDeploymentKind,\n): string {\n  return deploymentKind === \"local\" ? \"cli-local-dev\" : \"cli-anonymous-dev\";\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAAgD;AAChD,uBAIO;AACP,kBAAiB;AACjB,2BAA0B;AAC1B,yBAAmB;AACnB,oBAA2B;AAC3B,oBAA2B;AAC3B,oBAAqC;AAErC,eAAsB,gBACpB,KACA,MAaC;AACD,QAAM,EAAE,MAAM,IAAI;AAClB,QAAM,oBAAgB;AAAA,IACpB,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AACA,MAAI,GAAG,MAAM,eAAe,EAAE,WAAW,KAAK,CAAC;AAC/C,QAAM,wBAAoB,0BAAW,QAAQ,EAC1C,OAAO,KAAK,cAAc,EAC1B,OAAO,KAAK;AACf,QAAM,cAAc;AAAA,IAClB;AAAA,IACA,MAAM,MAAM,SAAS;AAAA,IACrB;AAAA,IACA,MAAM,KAAK,SAAS;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK;AAAA,IACL;AAAA,IACA,KAAK;AAAA,IACL;AAAA,IACA,YAAAA,QAAK,KAAK,eAAe,sBAAsB;AAAA,IAC/C;AAAA,IACA,mBAAmB,KAAK,cAAc;AAAA,IACtC,YAAAA,QAAK,KAAK,eAAe,8BAA8B;AAAA,EACzD;AACA,MAAI,KAAK,iBAAiB;AAGxB,QAAI,KAAK,mBAAmB,aAAa;AACvC,YAAM,WAAO,2CAAyB,GAAG;AACzC,UAAI,SAAS,MAAM;AACjB,oBAAY;AAAA,UACV;AAAA,UACA,KAAK,UAAU;AAAA,YACb,eAAe;AAAA,UACjB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAGA,MAAI;AACF,UAAM,SAAS,qBAAAC,QAAc,UAAU,KAAK,YAAY;AAAA,MACtD,GAAG;AAAA,MACH;AAAA,IACF,CAAC;AACD,QAAI,OAAO,WAAW,YAAY;AAChC,YAAM,UACJ;AACF,aAAO,IAAI,MAAM;AAAA,QACf,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,cAAc,IAAI;AAAA,UAChB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,WAAW,OAAO,WAAW,GAAG;AAC9B,YAAM,UAAU,2CAA2C,OAAO,MAAM,YAAY,OAAO,WAAW,OAAO,SAAS,OAAO,OAAO,SAAS,CAAC;AAC9I,aAAO,IAAI,MAAM;AAAA,QACf,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,cAAc,IAAI,mCAAqB,OAAO;AAAA,MAChD,CAAC;AAAA,IACH;AAAA,EACF,SAAS,GAAG;AACV,UAAM,UAAU,iCAAkC,EAAU,SAAS,CAAC;AACtE,WAAO,IAAI,MAAM;AAAA,MACf,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,cAAc,IAAI,mCAAqB,OAAO;AAAA,IAChD,CAAC;AAAA,EACH;AACA,QAAM,aAAa,GAAG,KAAK,UAAU,IAAI,YAAY,KAAK,GAAG,CAAC;AAC9D,iCAAW,KAAK,6BAA6B,UAAU,IAAI;AAC3D,QAAM,IAAI,qBAAAA,QACP,MAAM,KAAK,YAAY,aAAa;AAAA,IACnC,OAAO;AAAA,IACP,KAAK;AAAA,MACH,GAAG,QAAQ;AAAA,MACX,YAAY;AAAA,IACd;AAAA,EACF,CAAC,EACA,GAAG,QAAQ,CAAC,SAAS;AACpB,UAAM,MAAM,SAAS,OAAO,gBAAgB,aAAa,IAAI;AAC7D;AAAA,MACE;AAAA,MACA,wBAAwB,GAAG,oBAAoB,UAAU;AAAA,IAC3D;AAAA,EACF,CAAC;AACH,QAAM,gBAAgB,IAAI,gBAAgB,YAAY;AACpD,mCAAW,KAAK,kCAAkC,MAAM,KAAK,EAAE;AAC/D,MAAE,KAAK,SAAS;AAAA,EAClB,CAAC;AAED,QAAM,qBAAqB,KAAK;AAAA,IAC9B,WAAW,MAAM;AAAA,IACjB,gBAAgB,KAAK;AAAA,IACrB,aAAa;AAAA,EACf,CAAC;AAED,SAAO;AAAA,IACL;AAAA,EACF;AACF;AAGA,eAAsB,0BACpB,KACA,MAIe;AACf,iCAAW,KAAK,6BAA6B,KAAK,GAAG,aAAa;AAClE,MAAI;AACF,UAAM,OAAO,MAAM,MAAM,GAAG,KAAK,GAAG,gBAAgB;AACpD,QAAI,KAAK,WAAW,KAAK;AACvB,YAAM,OAAO,MAAM,KAAK,KAAK;AAC7B,UAAI,SAAS,KAAK,gBAAgB;AAChC,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB,6BAA6B,IAAI,kBAAkB,KAAK,GAAG;AAAA,QAC7E,CAAC;AAAA,MACH,OAAO;AACL;AAAA,MACF;AAAA,IACF,OAAO;AACL,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBAAgB,mDAAmD,KAAK,MAAM,IAAI,KAAK,UAAU;AAAA,MACnG,CAAC;AAAA,IACH;AAAA,EACF,QAAQ;AACN,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,uDAAuD,KAAK,GAAG;AAAA;AAAA,IACjF,CAAC;AAAA,EACH;AACF;AAGA,eAAsB,qBACpB,KACA,MAKe;AACf;AAAA,IACE;AAAA,IACA,oCAAoC,KAAK,SAAS;AAAA,EACpD;AACA,QAAM,gBAAgB,mBAAmB,KAAK,SAAS;AACvD,MAAI,kBAAkB;AACtB,MAAI,kBAAkB;AACtB,SAAO,mBAAmB,KAAK,aAAa;AAC1C,QAAI,CAAC,mBAAmB,kBAAkB,GAAG;AAC3C,qCAAW,KAAK,uCAAuC;AACvD,wBAAkB;AAAA,IACpB;AACA,QAAI;AACF,YAAM,OAAO,MAAM,MAAM,GAAG,aAAa,gBAAgB;AACzD,UAAI,KAAK,WAAW,KAAK;AACvB,cAAM,OAAO,MAAM,KAAK,KAAK;AAC7B,YAAI,SAAS,KAAK,gBAAgB;AAChC,iBAAO,MAAM,IAAI,MAAM;AAAA,YACrB,UAAU;AAAA,YACV,WAAW;AAAA,YACX,gBAAgB,6BAA6B,IAAI,gCAAgC,KAAK,SAAS;AAAA,UACjG,CAAC;AAAA,QACH,OAAO;AAEL;AAAA,QACF;AAAA,MACF,OAAO;AACL,cAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,GAAG,CAAC;AACvD,2BAAmB;AAAA,MACrB;AAAA,IACF,QAAQ;AACN,YAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,GAAG,CAAC;AACvD,yBAAmB;AAAA,IACrB;AAAA,EACF;AACA,QAAM,UAAU,uCAAuC,KAAK,SAAS,WAAW,KAAK,WAAW;AAChG,SAAO,MAAM,IAAI,MAAM;AAAA,IACrB,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,cAAc,IAAI,mCAAqB,OAAO;AAAA,EAChD,CAAC;AACH;AAEA,eAAsB,qBACpB,KACA,MAUA;AACA;AAAA,IACE;AAAA,IACA,oCAAoC,KAAK,MAAM,KAAK;AAAA,EACtD;AACA,MAAI,kBAAkB;AACtB,SAAO,kBAAkB,KAAK,aAAa;AACzC,UAAM,YAAY,UAAM,mBAAAC,SAAO,KAAK,MAAM,KAAK;AAC/C,UAAM,WACJ,KAAK,MAAM,SAAS,SAAY,SAAY,UAAM,mBAAAA,SAAO,KAAK,MAAM,IAAI;AAE1E,QAAI,cAAc,KAAK,MAAM,SAAS,aAAa,KAAK,MAAM,MAAM;AAClE;AAAA,IACF;AACA,QAAI;AACF,YAAM,mBAAmB,MAAM;AAAA,QAC7B,GAAG,mBAAmB,KAAK,MAAM,KAAK,CAAC;AAAA,MACzC;AACA,UAAI,iBAAiB,IAAI;AACvB,cAAM,eAAe,MAAM,iBAAiB,KAAK;AACjD,YAAI,iBAAiB,KAAK,gBAAgB;AACxC,cAAI,KAAK,uBAAuB;AAC9B;AAAA,UACF;AACA,iBAAO,MAAM,IAAI,MAAM;AAAA,YACrB,UAAU;AAAA,YACV,WAAW;AAAA,YACX,gBAAgB,6BAA6B,YAAY,gCAAgC,KAAK,MAAM,KAAK;AAAA,UAC3G,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF,SAAS,OAAY;AACnB,qCAAW,KAAK,yCAAyC,MAAM,OAAO,EAAE;AAExE;AAAA,IACF;AACA,UAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,GAAG,CAAC;AACvD,uBAAmB;AAAA,EACrB;AACA,SAAO,IAAI,MAAM;AAAA,IACf,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB,4CAA4C,KAAK,MAAM,KAAK;AAAA,EAC9E,CAAC;AACH;AAEO,SAAS,mBAAmB,WAA2B;AAC5D,SAAO,oBAAoB,SAAS;AACtC;AAEO,SAAS,mBACd,gBACQ;AACR,SAAO,mBAAmB,UAAU,kBAAkB;AACxD;", "names": ["path", "child_process", "detect"]}