{"version": 3, "sources": ["../../../../src/cli/lib/command.ts"], "sourcesContent": ["import { Command, Option } from \"@commander-js/extra-typings\";\nimport { OneoffCtx } from \"../../bundler/context.js\";\nimport { LogMode } from \"./logs.js\";\nimport {\n  CONVEX_DEPLOYMENT_ENV_VAR_NAME,\n  CONVEX_SELF_HOSTED_ADMIN_KEY_VAR_NAME,\n  CONVEX_SELF_HOSTED_URL_VAR_NAME,\n  parseInteger,\n  parsePositiveInteger,\n} from \"./utils/utils.js\";\n\ndeclare module \"@commander-js/extra-typings\" {\n  interface Command<Args extends any[] = [], Opts extends OptionValues = {}> {\n    /**\n     * For a command that talks to the configured dev deployment by default,\n     * add flags for talking to prod, preview, or other deployment in the same\n     * project.\n     *\n     * These flags are added to the end of `command` (ordering matters for `--help`\n     * output). `action` should look like \"Import data into\" because it is prefixed\n     * onto help strings.\n     *\n     * The options can be passed to `deploymentSelectionFromOptions`.\n     *\n     * NOTE: This method only exists at runtime if this file is imported.\n     * To help avoid this bug, this method takes in an `ActionDescription` which\n     * can only be constructed via `actionDescription` from this file.\n     */\n    addDeploymentSelectionOptions(action: ActionDescription): Command<\n      Args,\n      Opts & {\n        envFile?: string;\n        url?: string;\n        adminKey?: string;\n        prod?: boolean;\n        previewName?: string;\n        deploymentName?: string;\n      }\n    >;\n\n    /**\n     * Adds options for the `deploy` command.\n     */\n    addDeployOptions(): Command<\n      Args,\n      Opts & {\n        verbose?: boolean;\n        dryRun?: boolean;\n        yes?: boolean;\n        typecheck: \"enable\" | \"try\" | \"disable\";\n        typecheckComponents: boolean;\n        codegen: \"enable\" | \"disable\";\n        cmd?: string;\n        cmdUrlEnvVarName?: string;\n        debugBundlePath?: string;\n        debug?: boolean;\n        writePushRequest?: string;\n        liveComponentSources?: boolean;\n      }\n    >;\n\n    /**\n     * Adds options for `self-host` subcommands.\n     */\n    addSelfHostOptions(): Command<\n      Args,\n      Opts & {\n        url?: string;\n        adminKey?: string;\n        env?: string;\n      }\n    >;\n\n    /**\n     * Adds options and arguments for the `run` command.\n     */\n    addRunOptions(): Command<\n      [...Args, string, string | undefined],\n      Opts & {\n        watch?: boolean;\n        push?: boolean;\n        identity?: string;\n        typecheck: \"enable\" | \"try\" | \"disable\";\n        typecheckComponents: boolean;\n        codegen: \"enable\" | \"disable\";\n        component?: string;\n        liveComponentSources?: boolean;\n      }\n    >;\n\n    /**\n     * Adds options for the `import` command.\n     */\n    addImportOptions(): Command<\n      [...Args, string],\n      Opts & {\n        table?: string;\n        format?: \"csv\" | \"jsonLines\" | \"jsonArray\" | \"zip\";\n        replace?: boolean;\n        append?: boolean;\n        replaceAll?: boolean;\n        yes?: boolean;\n        component?: string;\n      }\n    >;\n\n    /**\n     * Adds options for the `export` command.\n     */\n    addExportOptions(): Command<\n      Args,\n      Opts & {\n        path: string;\n        includeFileStorage?: boolean;\n      }\n    >;\n\n    /**\n     * Adds options for the `data` command.\n     */\n    addDataOptions(): Command<\n      [...Args, string | undefined],\n      Opts & {\n        limit: number;\n        order: \"asc\" | \"desc\";\n        component?: string;\n      }\n    >;\n\n    /**\n     * Adds options for the `logs` command.\n     */\n    addLogsOptions(): Command<\n      Args,\n      Opts & {\n        history: number;\n        success: boolean;\n      }\n    >;\n\n    /**\n     * Adds options for the `network-test` command.\n     */\n    addNetworkTestOptions(): Command<\n      Args,\n      Opts & {\n        timeout?: string;\n        ipFamily?: string;\n        speedTest?: boolean;\n      }\n    >;\n  }\n}\n\nCommand.prototype.addDeploymentSelectionOptions = function (\n  action: ActionDescription,\n) {\n  return this.addOption(\n    new Option(\"--url <url>\")\n      .conflicts([\"--prod\", \"--preview-name\", \"--deployment-name\"])\n      .hideHelp(),\n  )\n    .addOption(new Option(\"--admin-key <adminKey>\").hideHelp())\n    .addOption(\n      new Option(\n        \"--env-file <envFile>\",\n        `Path to a custom file of environment variables, for choosing the \\\ndeployment, e.g. ${CONVEX_DEPLOYMENT_ENV_VAR_NAME} or ${CONVEX_SELF_HOSTED_URL_VAR_NAME}. \\\nSame format as .env.local or .env files, and overrides them.`,\n      ),\n    )\n    .addOption(\n      new Option(\n        \"--prod\",\n        action + \" this project's production deployment.\",\n      ).conflicts([\"--preview-name\", \"--deployment-name\", \"--url\"]),\n    )\n    .addOption(\n      new Option(\n        \"--preview-name <previewName>\",\n        action + \" the preview deployment with the given name.\",\n      ).conflicts([\"--prod\", \"--deployment-name\", \"--url\"]),\n    )\n    .addOption(\n      new Option(\n        \"--deployment-name <deploymentName>\",\n        action + \" the specified deployment.\",\n      ).conflicts([\"--prod\", \"--preview-name\", \"--url\"]),\n    ) as any;\n};\n\ndeclare const tag: unique symbol;\ntype ActionDescription = string & { readonly [tag]: \"noop\" };\nexport function actionDescription(action: string): ActionDescription {\n  return action as any;\n}\n\nexport async function normalizeDevOptions(\n  ctx: OneoffCtx,\n  cmdOptions: {\n    verbose?: boolean;\n    typecheck: \"enable\" | \"try\" | \"disable\";\n    typecheckComponents?: boolean;\n    codegen: \"enable\" | \"disable\";\n    once?: boolean;\n    untilSuccess: boolean;\n    run?: string;\n    runSh?: string;\n    runComponent?: string;\n    tailLogs?: string | true;\n    traceEvents: boolean;\n    debugBundlePath?: string;\n    debugNodeApis?: boolean;\n    liveComponentSources?: boolean;\n    while?: string;\n  },\n): Promise<{\n  verbose: boolean;\n  typecheck: \"enable\" | \"try\" | \"disable\";\n  typecheckComponents: boolean;\n  codegen: boolean;\n  once: boolean;\n  untilSuccess: boolean;\n  run?:\n    | { kind: \"function\"; name: string; component?: string }\n    | { kind: \"shell\"; command: string };\n  tailLogs: LogMode;\n  traceEvents: boolean;\n  debugBundlePath?: string;\n  debugNodeApis: boolean;\n  liveComponentSources: boolean;\n}> {\n  if (cmdOptions.runComponent && !cmdOptions.run) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: \"Can't specify `--run-component` option without `--run`\",\n    });\n  }\n\n  if (cmdOptions.debugBundlePath !== undefined && !cmdOptions.once) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: \"`--debug-bundle-path` can only be used with `--once`.\",\n    });\n  }\n  if (cmdOptions.debugNodeApis && !cmdOptions.once) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: \"`--debug-node-apis` can only be used with `--once`.\",\n    });\n  }\n\n  return {\n    verbose: !!cmdOptions.verbose,\n    typecheck: cmdOptions.typecheck,\n    typecheckComponents: !!cmdOptions.typecheckComponents,\n    codegen: cmdOptions.codegen === \"enable\",\n    once: !!cmdOptions.once,\n    untilSuccess: cmdOptions.untilSuccess,\n    run:\n      cmdOptions.run !== undefined\n        ? {\n            kind: \"function\",\n            name: cmdOptions.run,\n            component: cmdOptions.runComponent,\n          }\n        : cmdOptions.runSh !== undefined\n          ? {\n              kind: \"shell\",\n              command: cmdOptions.runSh,\n            }\n          : undefined,\n    tailLogs:\n      typeof cmdOptions.tailLogs === \"string\"\n        ? (cmdOptions.tailLogs as LogMode)\n        : \"pause-on-deploy\",\n    traceEvents: cmdOptions.traceEvents,\n    debugBundlePath: cmdOptions.debugBundlePath,\n    debugNodeApis: !!cmdOptions.debugNodeApis,\n    liveComponentSources: !!cmdOptions.liveComponentSources,\n  };\n}\n\nCommand.prototype.addDeployOptions = function () {\n  return this.option(\"-v, --verbose\", \"Show full listing of changes\")\n    .option(\n      \"--dry-run\",\n      \"Print out the generated configuration without deploying to your Convex deployment\",\n    )\n    .option(\"-y, --yes\", \"Skip confirmation prompt when running locally\")\n    .addOption(\n      new Option(\n        \"--typecheck <mode>\",\n        `Whether to check TypeScript files with \\`tsc --noEmit\\` before deploying.`,\n      )\n        .choices([\"enable\", \"try\", \"disable\"] as const)\n        .default(\"try\" as const),\n    )\n    .option(\n      \"--typecheck-components\",\n      \"Check TypeScript files within component implementations with `tsc --noEmit`.\",\n      false,\n    )\n    .addOption(\n      new Option(\n        \"--codegen <mode>\",\n        \"Whether to regenerate code in `convex/_generated/` before pushing.\",\n      )\n        .choices([\"enable\", \"disable\"] as const)\n        .default(\"enable\" as const),\n    )\n    .addOption(\n      new Option(\n        \"--cmd <command>\",\n        \"Command to run as part of deploying your app (e.g. `vite build`). This command can depend on the environment variables specified in `--cmd-url-env-var-name` being set.\",\n      ),\n    )\n    .addOption(\n      new Option(\n        \"--cmd-url-env-var-name <name>\",\n        \"Environment variable name to set Convex deployment URL (e.g. `VITE_CONVEX_URL`) when using `--cmd`\",\n      ),\n    )\n    .addOption(new Option(\"--debug-bundle-path <path>\").hideHelp())\n    .addOption(new Option(\"--debug\").hideHelp())\n    .addOption(new Option(\"--write-push-request <writePushRequest>\").hideHelp())\n    .addOption(new Option(\"--live-component-sources\").hideHelp());\n};\n\nCommand.prototype.addSelfHostOptions = function () {\n  return this.option(\n    \"--admin-key <adminKey>\",\n    `An admin key for the deployment. Can alternatively be set as \\`${CONVEX_SELF_HOSTED_ADMIN_KEY_VAR_NAME}\\` environment variable.`,\n  )\n    .option(\n      \"--url <url>\",\n      `The url of the deployment. Can alternatively be set as \\`${CONVEX_SELF_HOSTED_URL_VAR_NAME}\\` environment variable.`,\n    )\n    .option(\n      \"--env <env>\",\n      `Path to a custom file of environment variables, containing \\`${CONVEX_SELF_HOSTED_URL_VAR_NAME}\\` and \\`${CONVEX_SELF_HOSTED_ADMIN_KEY_VAR_NAME}\\`.`,\n    );\n};\n\nCommand.prototype.addRunOptions = function () {\n  return (\n    this.argument(\n      \"functionName\",\n      \"identifier of the function to run, like `listMessages` or `dir/file:myFunction`\",\n    )\n      .argument(\n        \"[args]\",\n        \"JSON-formatted arguments object to pass to the function.\",\n      )\n      .option(\n        \"-w, --watch\",\n        \"Watch a query, printing its result if the underlying data changes. Given function must be a query.\",\n      )\n      .option(\"--push\", \"Push code to deployment before running the function.\")\n      .addOption(\n        new Option(\n          \"--identity <identity>\",\n          'JSON-formatted UserIdentity object, e.g. \\'{ name: \"John\", address: \"0x123\" }\\'',\n        ),\n      )\n      // For backwards compatibility we still support --no-push which is a noop\n      .addOption(new Option(\"--no-push\").hideHelp())\n      // Options for the deploy that --push does\n      .addOption(\n        new Option(\n          \"--typecheck <mode>\",\n          `Whether to check TypeScript files with \\`tsc --noEmit\\`.`,\n        )\n          .choices([\"enable\", \"try\", \"disable\"] as const)\n          .default(\"try\" as const),\n      )\n      .option(\n        \"--typecheck-components\",\n        \"Check TypeScript files within component implementations with `tsc --noEmit`.\",\n        false,\n      )\n      .addOption(\n        new Option(\n          \"--codegen <mode>\",\n          \"Regenerate code in `convex/_generated/`\",\n        )\n          .choices([\"enable\", \"disable\"] as const)\n          .default(\"enable\" as const),\n      )\n      .addOption(\n        new Option(\n          \"--component <path>\",\n          \"Path to the component in the component tree defined in convex.config.ts. \" +\n            \"Components are a beta feature. This flag is unstable and may change in subsequent releases.\",\n        ),\n      )\n      .addOption(new Option(\"--live-component-sources\").hideHelp())\n  );\n};\n\nCommand.prototype.addImportOptions = function () {\n  return this.argument(\"<path>\", \"Path to the input file\")\n    .addOption(\n      new Option(\n        \"--table <table>\",\n        \"Destination table name. Required if format is csv, jsonLines, or jsonArray. Not supported if format is zip.\",\n      ),\n    )\n    .addOption(\n      new Option(\n        \"--replace\",\n        \"Replace all existing data in any of the imported tables\",\n      )\n        .conflicts(\"--append\")\n        .conflicts(\"--replace-all\"),\n    )\n    .addOption(\n      new Option(\"--append\", \"Append imported data to any existing tables\")\n        .conflicts(\"--replace-all\")\n        .conflicts(\"--replace\"),\n    )\n    .addOption(\n      new Option(\n        \"--replace-all\",\n        \"Replace all existing data in the deployment with the imported tables,\\n\" +\n          \"  deleting tables that don't appear in the import file or the schema,\\n\" +\n          \"  and clearing tables that appear in the schema but not in the import file\",\n      )\n        .conflicts(\"--append\")\n        .conflicts(\"--replace\"),\n    )\n    .option(\n      \"-y, --yes\",\n      \"Skip confirmation prompt when import leads to deleting existing documents\",\n    )\n    .addOption(\n      new Option(\n        \"--format <format>\",\n        \"Input file format. This flag is only required if the filename is missing an extension.\\n\" +\n          \"- CSV files must have a header, and each row's entries are interpreted either as a (floating point) number or a string.\\n\" +\n          \"- JSON files must be an array of JSON objects.\\n\" +\n          \"- JSONLines files must have a JSON object per line.\\n\" +\n          \"- ZIP files must have one directory per table, containing <table>/documents.jsonl. Snapshot exports from the Convex dashboard have this format.\",\n      ).choices([\"csv\", \"jsonLines\", \"jsonArray\", \"zip\"]),\n    )\n    .addOption(\n      new Option(\n        \"--component <path>\",\n        \"Path to the component in the component tree defined in convex.config.ts\",\n      ),\n    );\n};\n\nCommand.prototype.addExportOptions = function () {\n  return this.requiredOption(\n    \"--path <zipFilePath>\",\n    \"Exports data into a ZIP file at this path, which may be a directory or unoccupied .zip path\",\n  ).addOption(\n    new Option(\n      \"--include-file-storage\",\n      \"Includes stored files (https://dashboard.convex.dev/deployment/files) in a _storage folder within the ZIP file\",\n    ),\n  );\n};\n\nCommand.prototype.addDataOptions = function () {\n  return this.addOption(\n    new Option(\n      \"--limit <n>\",\n      \"List only the `n` the most recently created documents.\",\n    )\n      .default(100)\n      .argParser(parsePositiveInteger),\n  )\n    .addOption(\n      new Option(\n        \"--order <choice>\",\n        \"Order the documents by their `_creationTime`.\",\n      )\n        .choices([\"asc\", \"desc\"])\n        .default(\"desc\"),\n    )\n    .addOption(\n      new Option(\n        \"--component <path>\",\n        \"Path to the component in the component tree defined in convex.config.ts.\\n\" +\n          \"  By default, inspects data in the root component\",\n      ).hideHelp(),\n    )\n    .argument(\"[table]\", \"If specified, list documents in this table.\");\n};\n\nCommand.prototype.addLogsOptions = function () {\n  return this.option(\n    \"--history [n]\",\n    \"Show `n` most recent logs. Defaults to showing all available logs.\",\n    parseInteger,\n  ).option(\n    \"--success\",\n    \"Print a log line for every successful function execution\",\n    false,\n  );\n};\n\nCommand.prototype.addNetworkTestOptions = function () {\n  return this.addOption(\n    new Option(\n      \"--timeout <timeout>\",\n      \"Timeout in seconds for the network test (default: 30).\",\n    ),\n  )\n    .addOption(\n      new Option(\n        \"--ip-family <ipFamily>\",\n        \"IP family to use (ipv4, ipv6, or auto)\",\n      ),\n    )\n    .addOption(\n      new Option(\n        \"--speed-test\",\n        \"Perform a large echo test to measure network speed.\",\n      ),\n    );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BAAgC;AAGhC,mBAMO;AAiJP,6BAAQ,UAAU,gCAAgC,SAChD,QACA;AACA,SAAO,KAAK;AAAA,IACV,IAAI,4BAAO,aAAa,EACrB,UAAU,CAAC,UAAU,kBAAkB,mBAAmB,CAAC,EAC3D,SAAS;AAAA,EACd,EACG,UAAU,IAAI,4BAAO,wBAAwB,EAAE,SAAS,CAAC,EACzD;AAAA,IACC,IAAI;AAAA,MACF;AAAA,MACA,qFACW,2CAA8B,OAAO,4CAA+B;AAAA,IAEjF;AAAA,EACF,EACC;AAAA,IACC,IAAI;AAAA,MACF;AAAA,MACA,SAAS;AAAA,IACX,EAAE,UAAU,CAAC,kBAAkB,qBAAqB,OAAO,CAAC;AAAA,EAC9D,EACC;AAAA,IACC,IAAI;AAAA,MACF;AAAA,MACA,SAAS;AAAA,IACX,EAAE,UAAU,CAAC,UAAU,qBAAqB,OAAO,CAAC;AAAA,EACtD,EACC;AAAA,IACC,IAAI;AAAA,MACF;AAAA,MACA,SAAS;AAAA,IACX,EAAE,UAAU,CAAC,UAAU,kBAAkB,OAAO,CAAC;AAAA,EACnD;AACJ;AAIO,SAAS,kBAAkB,QAAmC;AACnE,SAAO;AACT;AAEA,eAAsB,oBACpB,KACA,YAgCC;AACD,MAAI,WAAW,gBAAgB,CAAC,WAAW,KAAK;AAC9C,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAEA,MAAI,WAAW,oBAAoB,UAAa,CAAC,WAAW,MAAM;AAChE,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACA,MAAI,WAAW,iBAAiB,CAAC,WAAW,MAAM;AAChD,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAEA,SAAO;AAAA,IACL,SAAS,CAAC,CAAC,WAAW;AAAA,IACtB,WAAW,WAAW;AAAA,IACtB,qBAAqB,CAAC,CAAC,WAAW;AAAA,IAClC,SAAS,WAAW,YAAY;AAAA,IAChC,MAAM,CAAC,CAAC,WAAW;AAAA,IACnB,cAAc,WAAW;AAAA,IACzB,KACE,WAAW,QAAQ,SACf;AAAA,MACE,MAAM;AAAA,MACN,MAAM,WAAW;AAAA,MACjB,WAAW,WAAW;AAAA,IACxB,IACA,WAAW,UAAU,SACnB;AAAA,MACE,MAAM;AAAA,MACN,SAAS,WAAW;AAAA,IACtB,IACA;AAAA,IACR,UACE,OAAO,WAAW,aAAa,WAC1B,WAAW,WACZ;AAAA,IACN,aAAa,WAAW;AAAA,IACxB,iBAAiB,WAAW;AAAA,IAC5B,eAAe,CAAC,CAAC,WAAW;AAAA,IAC5B,sBAAsB,CAAC,CAAC,WAAW;AAAA,EACrC;AACF;AAEA,6BAAQ,UAAU,mBAAmB,WAAY;AAC/C,SAAO,KAAK,OAAO,iBAAiB,8BAA8B,EAC/D;AAAA,IACC;AAAA,IACA;AAAA,EACF,EACC,OAAO,aAAa,+CAA+C,EACnE;AAAA,IACC,IAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,EACG,QAAQ,CAAC,UAAU,OAAO,SAAS,CAAU,EAC7C,QAAQ,KAAc;AAAA,EAC3B,EACC;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,EACF,EACC;AAAA,IACC,IAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,EACG,QAAQ,CAAC,UAAU,SAAS,CAAU,EACtC,QAAQ,QAAiB;AAAA,EAC9B,EACC;AAAA,IACC,IAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAAA,EACF,EACC;AAAA,IACC,IAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAAA,EACF,EACC,UAAU,IAAI,4BAAO,4BAA4B,EAAE,SAAS,CAAC,EAC7D,UAAU,IAAI,4BAAO,SAAS,EAAE,SAAS,CAAC,EAC1C,UAAU,IAAI,4BAAO,yCAAyC,EAAE,SAAS,CAAC,EAC1E,UAAU,IAAI,4BAAO,0BAA0B,EAAE,SAAS,CAAC;AAChE;AAEA,6BAAQ,UAAU,qBAAqB,WAAY;AACjD,SAAO,KAAK;AAAA,IACV;AAAA,IACA,kEAAkE,kDAAqC;AAAA,EACzG,EACG;AAAA,IACC;AAAA,IACA,4DAA4D,4CAA+B;AAAA,EAC7F,EACC;AAAA,IACC;AAAA,IACA,gEAAgE,4CAA+B,YAAY,kDAAqC;AAAA,EAClJ;AACJ;AAEA,6BAAQ,UAAU,gBAAgB,WAAY;AAC5C,SACE,KAAK;AAAA,IACH;AAAA,IACA;AAAA,EACF,EACG;AAAA,IACC;AAAA,IACA;AAAA,EACF,EACC;AAAA,IACC;AAAA,IACA;AAAA,EACF,EACC,OAAO,UAAU,sDAAsD,EACvE;AAAA,IACC,IAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAAA,EACF,EAEC,UAAU,IAAI,4BAAO,WAAW,EAAE,SAAS,CAAC,EAE5C;AAAA,IACC,IAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,EACG,QAAQ,CAAC,UAAU,OAAO,SAAS,CAAU,EAC7C,QAAQ,KAAc;AAAA,EAC3B,EACC;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,EACF,EACC;AAAA,IACC,IAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,EACG,QAAQ,CAAC,UAAU,SAAS,CAAU,EACtC,QAAQ,QAAiB;AAAA,EAC9B,EACC;AAAA,IACC,IAAI;AAAA,MACF;AAAA,MACA;AAAA,IAEF;AAAA,EACF,EACC,UAAU,IAAI,4BAAO,0BAA0B,EAAE,SAAS,CAAC;AAElE;AAEA,6BAAQ,UAAU,mBAAmB,WAAY;AAC/C,SAAO,KAAK,SAAS,UAAU,wBAAwB,EACpD;AAAA,IACC,IAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAAA,EACF,EACC;AAAA,IACC,IAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,EACG,UAAU,UAAU,EACpB,UAAU,eAAe;AAAA,EAC9B,EACC;AAAA,IACC,IAAI,4BAAO,YAAY,6CAA6C,EACjE,UAAU,eAAe,EACzB,UAAU,WAAW;AAAA,EAC1B,EACC;AAAA,IACC,IAAI;AAAA,MACF;AAAA,MACA;AAAA,IAGF,EACG,UAAU,UAAU,EACpB,UAAU,WAAW;AAAA,EAC1B,EACC;AAAA,IACC;AAAA,IACA;AAAA,EACF,EACC;AAAA,IACC,IAAI;AAAA,MACF;AAAA,MACA;AAAA,IAKF,EAAE,QAAQ,CAAC,OAAO,aAAa,aAAa,KAAK,CAAC;AAAA,EACpD,EACC;AAAA,IACC,IAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACJ;AAEA,6BAAQ,UAAU,mBAAmB,WAAY;AAC/C,SAAO,KAAK;AAAA,IACV;AAAA,IACA;AAAA,EACF,EAAE;AAAA,IACA,IAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAEA,6BAAQ,UAAU,iBAAiB,WAAY;AAC7C,SAAO,KAAK;AAAA,IACV,IAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,EACG,QAAQ,GAAG,EACX,UAAU,iCAAoB;AAAA,EACnC,EACG;AAAA,IACC,IAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,EACG,QAAQ,CAAC,OAAO,MAAM,CAAC,EACvB,QAAQ,MAAM;AAAA,EACnB,EACC;AAAA,IACC,IAAI;AAAA,MACF;AAAA,MACA;AAAA,IAEF,EAAE,SAAS;AAAA,EACb,EACC,SAAS,WAAW,6CAA6C;AACtE;AAEA,6BAAQ,UAAU,iBAAiB,WAAY;AAC7C,SAAO,KAAK;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EACF,EAAE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,6BAAQ,UAAU,wBAAwB,WAAY;AACpD,SAAO,KAAK;AAAA,IACV,IAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAAA,EACF,EACG;AAAA,IACC,IAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAAA,EACF,EACC;AAAA,IACC,IAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACJ;", "names": []}