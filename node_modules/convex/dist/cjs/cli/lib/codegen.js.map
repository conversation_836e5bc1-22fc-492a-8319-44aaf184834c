{"version": 3, "sources": ["../../../../src/cli/lib/codegen.ts"], "sourcesContent": ["import path from \"path\";\nimport prettier from \"prettier\";\nimport { withTmpDir, TempDir } from \"../../bundler/fs.js\";\nimport { entryPoints } from \"../../bundler/index.js\";\nimport { apiCodegen } from \"../codegen_templates/api.js\";\nimport { apiCjsCodegen } from \"../codegen_templates/api_cjs.js\";\nimport {\n  dynamicDataModelDTS,\n  noSchemaDataModelDTS,\n  staticDataModelDTS,\n} from \"../codegen_templates/dataModel.js\";\nimport { readmeCodegen } from \"../codegen_templates/readme.js\";\nimport { serverCodegen } from \"../codegen_templates/server.js\";\nimport { tsconfigCodegen } from \"../codegen_templates/tsconfig.js\";\nimport {\n  Context,\n  logError,\n  logMessage,\n  logOutput,\n  logVerbose,\n} from \"../../bundler/context.js\";\nimport { typeCheckFunctionsInMode, TypeCheckMode } from \"./typecheck.js\";\nimport { configFilepath, readProjectConfig } from \"./config.js\";\nimport { recursivelyDelete } from \"./fsUtils.js\";\nimport {\n  componentServerDTS,\n  componentServerJS,\n  componentServerStubDTS,\n} from \"../codegen_templates/component_server.js\";\nimport { ComponentDirectory } from \"./components/definition/directoryStructure.js\";\nimport { StartPushResponse } from \"./deployApi/startPush.js\";\nimport {\n  componentApiDTS,\n  componentApiJs,\n  componentApiStubDTS,\n  rootComponentApiCJS,\n} from \"../codegen_templates/component_api.js\";\nimport { functionsDir } from \"./utils/utils.js\";\n\nexport type CodegenOptions = {\n  url?: string;\n  adminKey?: string;\n  dryRun: boolean;\n  debug: boolean;\n  typecheck: TypeCheckMode;\n  init: boolean;\n  commonjs: boolean;\n  liveComponentSources: boolean;\n  debugNodeApis: boolean;\n};\n\nexport async function doCodegenForNewProject(ctx: Context) {\n  const { projectConfig: existingProjectConfig } = await readProjectConfig(ctx);\n  const configPath = await configFilepath(ctx);\n  const functionsPath = functionsDir(configPath, existingProjectConfig);\n  await doInitCodegen(ctx, functionsPath, true);\n  // Disable typechecking since there isn't any code yet.\n  await doCodegen(ctx, functionsPath, \"disable\");\n}\n\nexport async function doInitCodegen(\n  ctx: Context,\n  functionsDir: string,\n  skipIfExists: boolean,\n  opts?: { dryRun?: boolean; debug?: boolean },\n): Promise<void> {\n  await prepareForCodegen(ctx, functionsDir, opts);\n  await withTmpDir(async (tmpDir) => {\n    await doReadmeCodegen(ctx, tmpDir, functionsDir, skipIfExists, opts);\n    await doTsconfigCodegen(ctx, tmpDir, functionsDir, skipIfExists, opts);\n  });\n}\n\nasync function prepareForCodegen(\n  ctx: Context,\n  functionsDir: string,\n  opts?: { dryRun?: boolean },\n) {\n  // Delete the old _generated.ts because v0.1.2 used to put the react generated\n  // code there\n  const legacyCodegenPath = path.join(functionsDir, \"_generated.ts\");\n  if (ctx.fs.exists(legacyCodegenPath)) {\n    if (opts?.dryRun) {\n      logError(\n        ctx,\n        `Command would delete legacy codegen file: ${legacyCodegenPath}}`,\n      );\n    } else {\n      logError(ctx, `Deleting legacy codegen file: ${legacyCodegenPath}}`);\n      ctx.fs.unlink(legacyCodegenPath);\n    }\n  }\n\n  // Create the codegen dir if it doesn't already exist.\n  const codegenDir = path.join(functionsDir, \"_generated\");\n  ctx.fs.mkdir(codegenDir, { allowExisting: true, recursive: true });\n  return codegenDir;\n}\n\nexport async function doCodegen(\n  ctx: Context,\n  functionsDir: string,\n  typeCheckMode: TypeCheckMode,\n  opts?: { dryRun?: boolean; generateCommonJSApi?: boolean; debug?: boolean },\n) {\n  const { projectConfig } = await readProjectConfig(ctx);\n  const codegenDir = await prepareForCodegen(ctx, functionsDir, opts);\n\n  await withTmpDir(async (tmpDir) => {\n    // Write files in dependency order so a watching dev server doesn't\n    // see inconsistent results where a file we write imports from a\n    // file that doesn't exist yet. We'll collect all the paths we write\n    // and then delete any remaining paths at the end.\n    const writtenFiles = [];\n\n    // First, `dataModel.d.ts` imports from the developer's `schema.js` file.\n    const schemaFiles = await doDataModelCodegen(\n      ctx,\n      tmpDir,\n      functionsDir,\n      codegenDir,\n      opts,\n    );\n    writtenFiles.push(...schemaFiles);\n\n    // Next, the `server.d.ts` file imports from `dataModel.d.ts`.\n    const serverFiles = await doServerCodegen(ctx, tmpDir, codegenDir, opts);\n    writtenFiles.push(...serverFiles);\n\n    // The `api.d.ts` file imports from the developer's modules, which then\n    // import from `server.d.ts`. Note that there's a cycle here, since the\n    // developer's modules could also import from the `api.{js,d.ts}` files.\n    const apiFiles = await doApiCodegen(\n      ctx,\n      tmpDir,\n      functionsDir,\n      codegenDir,\n      opts?.generateCommonJSApi || projectConfig.generateCommonJSApi,\n      opts,\n    );\n    writtenFiles.push(...apiFiles);\n\n    // Cleanup any files that weren't written in this run.\n    for (const file of ctx.fs.listDir(codegenDir)) {\n      if (!writtenFiles.includes(file.name)) {\n        recursivelyDelete(ctx, path.join(codegenDir, file.name), opts);\n      }\n    }\n\n    // Generated code is updated, typecheck the query and mutation functions.\n    await typeCheckFunctionsInMode(ctx, typeCheckMode, functionsDir);\n  });\n}\n\nexport async function doInitialComponentCodegen(\n  ctx: Context,\n  tmpDir: TempDir,\n  componentDirectory: ComponentDirectory,\n  opts?: {\n    dryRun?: boolean;\n    generateCommonJSApi?: boolean;\n    debug?: boolean;\n    verbose?: boolean;\n  },\n) {\n  const { projectConfig } = await readProjectConfig(ctx);\n\n  // This component defined in a dist directory; it is probably in a node_module\n  // directory, installed from a package. It is stuck with the files it has.\n  // Heuristics for this:\n  // - component definition has a dist/ directory as an ancestor\n  // - component definition is a .js file\n  // - presence of .js.map files\n  // We may improve this heuristic.\n  const isPublishedPackage =\n    componentDirectory.definitionPath.endsWith(\".js\") &&\n    !componentDirectory.isRoot;\n  if (isPublishedPackage) {\n    if (opts?.verbose) {\n      logMessage(\n        ctx,\n        `skipping initial codegen for installed package ${componentDirectory.path}`,\n      );\n    }\n    return;\n  }\n\n  const codegenDir = await prepareForCodegen(\n    ctx,\n    componentDirectory.path,\n    opts,\n  );\n\n  // Write files in dependency order so a watching dev server doesn't\n  // see inconsistent results where a file we write imports from a\n  // file that doesn't exist yet. We'll collect all the paths we write\n  // and then delete any remaining paths at the end.\n  const writtenFiles = [];\n\n  // First, `dataModel.d.ts` imports from the developer's `schema.js` file.\n  const dataModelFiles = await doInitialComponentDataModelCodegen(\n    ctx,\n    tmpDir,\n    componentDirectory,\n    codegenDir,\n    opts,\n  );\n  writtenFiles.push(...dataModelFiles);\n\n  // Next, the `server.d.ts` file imports from `dataModel.d.ts`.\n  const serverFiles = await doInitialComponentServerCodegen(\n    ctx,\n    componentDirectory.isRoot,\n    tmpDir,\n    codegenDir,\n    opts,\n  );\n  writtenFiles.push(...serverFiles);\n\n  // The `api.d.ts` file imports from the developer's modules, which then\n  // import from `server.d.ts`. Note that there's a cycle here, since the\n  // developer's modules could also import from the `api.{js,d.ts}` files.\n  const apiFiles = await doInitialComponentApiCodegen(\n    ctx,\n    componentDirectory.isRoot,\n    tmpDir,\n    codegenDir,\n    opts?.generateCommonJSApi || projectConfig.generateCommonJSApi,\n    opts,\n  );\n  writtenFiles.push(...apiFiles);\n\n  // Cleanup any files that weren't written in this run.\n  for (const file of ctx.fs.listDir(codegenDir)) {\n    if (!writtenFiles.includes(file.name)) {\n      recursivelyDelete(ctx, path.join(codegenDir, file.name), opts);\n    }\n  }\n}\n\nexport async function doFinalComponentCodegen(\n  ctx: Context,\n  tmpDir: TempDir,\n  rootComponent: ComponentDirectory,\n  componentDirectory: ComponentDirectory,\n  startPushResponse: StartPushResponse,\n  opts?: {\n    dryRun?: boolean;\n    debug?: boolean;\n    generateCommonJSApi?: boolean;\n  },\n) {\n  const { projectConfig } = await readProjectConfig(ctx);\n\n  const isPublishedPackage =\n    componentDirectory.definitionPath.endsWith(\".js\") &&\n    !componentDirectory.isRoot;\n  if (isPublishedPackage) {\n    return;\n  }\n\n  const codegenDir = path.join(componentDirectory.path, \"_generated\");\n  ctx.fs.mkdir(codegenDir, { allowExisting: true, recursive: true });\n\n  // `dataModel.d.ts`, `server.d.ts` and `api.d.ts` depend on analyze results, where we\n  // replace the stub generated during initial codegen with a more precise type.\n  const hasSchemaFile = schemaFileExists(ctx, componentDirectory.path);\n  let dataModelContents: string;\n  if (hasSchemaFile) {\n    if (projectConfig.codegen.staticDataModel) {\n      dataModelContents = await staticDataModelDTS(\n        ctx,\n        startPushResponse,\n        rootComponent,\n        componentDirectory,\n      );\n    } else {\n      dataModelContents = dynamicDataModelDTS();\n    }\n  } else {\n    dataModelContents = noSchemaDataModelDTS();\n  }\n  const dataModelDTSPath = path.join(codegenDir, \"dataModel.d.ts\");\n  await writeFormattedFile(\n    ctx,\n    tmpDir,\n    dataModelContents,\n    \"typescript\",\n    dataModelDTSPath,\n    opts,\n  );\n\n  const serverDTSPath = path.join(codegenDir, \"server.d.ts\");\n  const serverContents = await componentServerDTS(componentDirectory);\n  await writeFormattedFile(\n    ctx,\n    tmpDir,\n    serverContents,\n    \"typescript\",\n    serverDTSPath,\n    opts,\n  );\n\n  const apiDTSPath = path.join(codegenDir, \"api.d.ts\");\n  const apiContents = await componentApiDTS(\n    ctx,\n    startPushResponse,\n    rootComponent,\n    componentDirectory,\n    { staticApi: projectConfig.codegen.staticApi },\n  );\n  await writeFormattedFile(\n    ctx,\n    tmpDir,\n    apiContents,\n    \"typescript\",\n    apiDTSPath,\n    opts,\n  );\n\n  if (opts?.generateCommonJSApi || projectConfig.generateCommonJSApi) {\n    const apiCjsDTSPath = path.join(codegenDir, \"api_cjs.d.ts\");\n    await writeFormattedFile(\n      ctx,\n      tmpDir,\n      apiContents,\n      \"typescript\",\n      apiCjsDTSPath,\n      opts,\n    );\n  }\n}\n\nasync function doReadmeCodegen(\n  ctx: Context,\n  tmpDir: TempDir,\n  functionsDir: string,\n  skipIfExists: boolean,\n  opts?: { dryRun?: boolean; debug?: boolean },\n) {\n  const readmePath = path.join(functionsDir, \"README.md\");\n  if (skipIfExists && ctx.fs.exists(readmePath)) {\n    logVerbose(ctx, `Not overwriting README.md.`);\n    return;\n  }\n  await writeFormattedFile(\n    ctx,\n    tmpDir,\n    readmeCodegen(),\n    \"markdown\",\n    readmePath,\n    opts,\n  );\n}\n\nasync function doTsconfigCodegen(\n  ctx: Context,\n  tmpDir: TempDir,\n  functionsDir: string,\n  skipIfExists: boolean,\n  opts?: { dryRun?: boolean; debug?: boolean },\n) {\n  const tsconfigPath = path.join(functionsDir, \"tsconfig.json\");\n  if (skipIfExists && ctx.fs.exists(tsconfigPath)) {\n    logVerbose(ctx, `Not overwriting tsconfig.json.`);\n    return;\n  }\n  await writeFormattedFile(\n    ctx,\n    tmpDir,\n    tsconfigCodegen(),\n    \"json\",\n    tsconfigPath,\n    opts,\n  );\n}\n\nfunction schemaFileExists(ctx: Context, functionsDir: string) {\n  let schemaPath = path.join(functionsDir, \"schema.ts\");\n  let hasSchemaFile = ctx.fs.exists(schemaPath);\n  if (!hasSchemaFile) {\n    schemaPath = path.join(functionsDir, \"schema.js\");\n    hasSchemaFile = ctx.fs.exists(schemaPath);\n  }\n  return hasSchemaFile;\n}\n\nasync function doDataModelCodegen(\n  ctx: Context,\n  tmpDir: TempDir,\n  functionsDir: string,\n  codegenDir: string,\n  opts?: { dryRun?: boolean; debug?: boolean },\n) {\n  const hasSchemaFile = schemaFileExists(ctx, functionsDir);\n  const schemaContent = hasSchemaFile\n    ? dynamicDataModelDTS()\n    : noSchemaDataModelDTS();\n\n  await writeFormattedFile(\n    ctx,\n    tmpDir,\n    schemaContent,\n    \"typescript\",\n    path.join(codegenDir, \"dataModel.d.ts\"),\n    opts,\n  );\n  return [\"dataModel.d.ts\"];\n}\n\nasync function doServerCodegen(\n  ctx: Context,\n  tmpDir: TempDir,\n  codegenDir: string,\n  opts?: { dryRun?: boolean; debug?: boolean },\n) {\n  const serverContent = serverCodegen();\n  await writeFormattedFile(\n    ctx,\n    tmpDir,\n    serverContent.JS,\n    \"typescript\",\n    path.join(codegenDir, \"server.js\"),\n    opts,\n  );\n\n  await writeFormattedFile(\n    ctx,\n    tmpDir,\n    serverContent.DTS,\n    \"typescript\",\n    path.join(codegenDir, \"server.d.ts\"),\n    opts,\n  );\n\n  return [\"server.js\", \"server.d.ts\"];\n}\n\nasync function doInitialComponentServerCodegen(\n  ctx: Context,\n  isRoot: boolean,\n  tmpDir: TempDir,\n  codegenDir: string,\n  opts?: { dryRun?: boolean; debug?: boolean },\n) {\n  await writeFormattedFile(\n    ctx,\n    tmpDir,\n    componentServerJS(),\n    \"typescript\",\n    path.join(codegenDir, \"server.js\"),\n    opts,\n  );\n\n  // Don't write our stub if the file already exists: It probably\n  // has better type information than this stub.\n  const serverDTSPath = path.join(codegenDir, \"server.d.ts\");\n  if (!ctx.fs.exists(serverDTSPath)) {\n    await writeFormattedFile(\n      ctx,\n      tmpDir,\n      componentServerStubDTS(isRoot),\n      \"typescript\",\n      path.join(codegenDir, \"server.d.ts\"),\n      opts,\n    );\n  }\n\n  return [\"server.js\", \"server.d.ts\"];\n}\n\nasync function doInitialComponentDataModelCodegen(\n  ctx: Context,\n  tmpDir: TempDir,\n  componentDirectory: ComponentDirectory,\n  codegenDir: string,\n  opts?: { dryRun?: boolean; debug?: boolean },\n) {\n  const hasSchemaFile = schemaFileExists(ctx, componentDirectory.path);\n  const dataModelContext = hasSchemaFile\n    ? dynamicDataModelDTS()\n    : noSchemaDataModelDTS();\n  const dataModelPath = path.join(codegenDir, \"dataModel.d.ts\");\n\n  // Don't write our stub if the file already exists, since it may have\n  // better type information from `doFinalComponentDataModelCodegen`.\n  if (!ctx.fs.exists(dataModelPath)) {\n    await writeFormattedFile(\n      ctx,\n      tmpDir,\n      dataModelContext,\n      \"typescript\",\n      dataModelPath,\n      opts,\n    );\n  }\n  return [\"dataModel.d.ts\"];\n}\n\nasync function doInitialComponentApiCodegen(\n  ctx: Context,\n  isRoot: boolean,\n  tmpDir: TempDir,\n  codegenDir: string,\n  generateCommonJSApi: boolean,\n  opts?: { dryRun?: boolean; debug?: boolean },\n) {\n  const apiJS = componentApiJs();\n  await writeFormattedFile(\n    ctx,\n    tmpDir,\n    apiJS,\n    \"typescript\",\n    path.join(codegenDir, \"api.js\"),\n    opts,\n  );\n\n  // Don't write the `.d.ts` stub if it already exists.\n  const apiDTSPath = path.join(codegenDir, \"api.d.ts\");\n  const apiStubDTS = componentApiStubDTS();\n  if (!ctx.fs.exists(apiDTSPath)) {\n    await writeFormattedFile(\n      ctx,\n      tmpDir,\n      apiStubDTS,\n      \"typescript\",\n      apiDTSPath,\n      opts,\n    );\n  }\n\n  const writtenFiles = [\"api.js\", \"api.d.ts\"];\n\n  if (generateCommonJSApi && isRoot) {\n    const apiCjsJS = rootComponentApiCJS();\n    await writeFormattedFile(\n      ctx,\n      tmpDir,\n      apiCjsJS,\n      \"typescript\",\n      path.join(codegenDir, \"api_cjs.cjs\"),\n      opts,\n    );\n\n    const cjsStubPath = path.join(codegenDir, \"api_cjs.d.cts\");\n    if (!ctx.fs.exists(cjsStubPath)) {\n      await writeFormattedFile(\n        ctx,\n        tmpDir,\n        apiStubDTS,\n        \"typescript\",\n        cjsStubPath,\n        opts,\n      );\n    }\n    writtenFiles.push(\"api_cjs.cjs\", \"api_cjs.d.cts\");\n  }\n\n  return writtenFiles;\n}\n\nasync function doApiCodegen(\n  ctx: Context,\n  tmpDir: TempDir,\n  functionsDir: string,\n  codegenDir: string,\n  generateCommonJSApi: boolean,\n  opts?: { dryRun?: boolean; debug?: boolean },\n) {\n  const absModulePaths = await entryPoints(ctx, functionsDir);\n  const modulePaths = absModulePaths.map((p) => path.relative(functionsDir, p));\n\n  const apiContent = apiCodegen(modulePaths);\n  await writeFormattedFile(\n    ctx,\n    tmpDir,\n    apiContent.JS,\n    \"typescript\",\n    path.join(codegenDir, \"api.js\"),\n    opts,\n  );\n  await writeFormattedFile(\n    ctx,\n    tmpDir,\n    apiContent.DTS,\n    \"typescript\",\n    path.join(codegenDir, \"api.d.ts\"),\n    opts,\n  );\n  const writtenFiles = [\"api.js\", \"api.d.ts\"];\n\n  if (generateCommonJSApi) {\n    const apiCjsContent = apiCjsCodegen(modulePaths);\n    await writeFormattedFile(\n      ctx,\n      tmpDir,\n      apiCjsContent.JS,\n      \"typescript\",\n      path.join(codegenDir, \"api_cjs.cjs\"),\n      opts,\n    );\n    await writeFormattedFile(\n      ctx,\n      tmpDir,\n      apiCjsContent.DTS,\n      \"typescript\",\n      path.join(codegenDir, \"api_cjs.d.cts\"),\n      opts,\n    );\n    writtenFiles.push(\"api_cjs.cjs\", \"api_cjs.d.cts\");\n  }\n\n  return writtenFiles;\n}\n\nasync function writeFormattedFile(\n  ctx: Context,\n  tmpDir: TempDir,\n  contents: string,\n  filetype: string,\n  destination: string,\n  options?: {\n    dryRun?: boolean;\n    debug?: boolean;\n  },\n) {\n  // Run prettier so we don't have to think about formatting!\n  //\n  // This is a little sketchy because we are using the default prettier config\n  // (not our user's one) but it's better than nothing.\n  const formattedContents = await prettier.format(contents, {\n    parser: filetype,\n    pluginSearchDirs: false,\n  });\n  if (options?.debug) {\n    // NB: The `test_codegen_projects_are_up_to_date` smoke test depends\n    // on this output format.\n    logOutput(ctx, `# ${path.resolve(destination)}`);\n    logOutput(ctx, formattedContents);\n    return;\n  }\n  try {\n    const existing = ctx.fs.readUtf8File(destination);\n    if (existing === formattedContents) {\n      return;\n    }\n  } catch (err: any) {\n    if (err.code !== \"ENOENT\") {\n      // eslint-disable-next-line no-restricted-syntax\n      throw err;\n    }\n  }\n  if (options?.dryRun) {\n    logOutput(ctx, `Command would write file: ${destination}`);\n    return;\n  }\n  const tmpPath = tmpDir.writeUtf8File(formattedContents);\n  ctx.fs.swapTmpFile(tmpPath, destination);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAiB;AACjB,sBAAqB;AACrB,gBAAoC;AACpC,qBAA4B;AAC5B,iBAA2B;AAC3B,qBAA8B;AAC9B,uBAIO;AACP,oBAA8B;AAC9B,oBAA8B;AAC9B,sBAAgC;AAChC,qBAMO;AACP,uBAAwD;AACxD,oBAAkD;AAClD,qBAAkC;AAClC,8BAIO;AAGP,2BAKO;AACP,mBAA6B;AAc7B,eAAsB,uBAAuB,KAAc;AACzD,QAAM,EAAE,eAAe,sBAAsB,IAAI,UAAM,iCAAkB,GAAG;AAC5E,QAAM,aAAa,UAAM,8BAAe,GAAG;AAC3C,QAAM,oBAAgB,2BAAa,YAAY,qBAAqB;AACpE,QAAM,cAAc,KAAK,eAAe,IAAI;AAE5C,QAAM,UAAU,KAAK,eAAe,SAAS;AAC/C;AAEA,eAAsB,cACpB,KACAA,eACA,cACA,MACe;AACf,QAAM,kBAAkB,KAAKA,eAAc,IAAI;AAC/C,YAAM,sBAAW,OAAO,WAAW;AACjC,UAAM,gBAAgB,KAAK,QAAQA,eAAc,cAAc,IAAI;AACnE,UAAM,kBAAkB,KAAK,QAAQA,eAAc,cAAc,IAAI;AAAA,EACvE,CAAC;AACH;AAEA,eAAe,kBACb,KACAA,eACA,MACA;AAGA,QAAM,oBAAoB,YAAAC,QAAK,KAAKD,eAAc,eAAe;AACjE,MAAI,IAAI,GAAG,OAAO,iBAAiB,GAAG;AACpC,QAAI,MAAM,QAAQ;AAChB;AAAA,QACE;AAAA,QACA,6CAA6C,iBAAiB;AAAA,MAChE;AAAA,IACF,OAAO;AACL,mCAAS,KAAK,iCAAiC,iBAAiB,GAAG;AACnE,UAAI,GAAG,OAAO,iBAAiB;AAAA,IACjC;AAAA,EACF;AAGA,QAAM,aAAa,YAAAC,QAAK,KAAKD,eAAc,YAAY;AACvD,MAAI,GAAG,MAAM,YAAY,EAAE,eAAe,MAAM,WAAW,KAAK,CAAC;AACjE,SAAO;AACT;AAEA,eAAsB,UACpB,KACAA,eACA,eACA,MACA;AACA,QAAM,EAAE,cAAc,IAAI,UAAM,iCAAkB,GAAG;AACrD,QAAM,aAAa,MAAM,kBAAkB,KAAKA,eAAc,IAAI;AAElE,YAAM,sBAAW,OAAO,WAAW;AAKjC,UAAM,eAAe,CAAC;AAGtB,UAAM,cAAc,MAAM;AAAA,MACxB;AAAA,MACA;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,iBAAa,KAAK,GAAG,WAAW;AAGhC,UAAM,cAAc,MAAM,gBAAgB,KAAK,QAAQ,YAAY,IAAI;AACvE,iBAAa,KAAK,GAAG,WAAW;AAKhC,UAAM,WAAW,MAAM;AAAA,MACrB;AAAA,MACA;AAAA,MACAA;AAAA,MACA;AAAA,MACA,MAAM,uBAAuB,cAAc;AAAA,MAC3C;AAAA,IACF;AACA,iBAAa,KAAK,GAAG,QAAQ;AAG7B,eAAW,QAAQ,IAAI,GAAG,QAAQ,UAAU,GAAG;AAC7C,UAAI,CAAC,aAAa,SAAS,KAAK,IAAI,GAAG;AACrC,8CAAkB,KAAK,YAAAC,QAAK,KAAK,YAAY,KAAK,IAAI,GAAG,IAAI;AAAA,MAC/D;AAAA,IACF;AAGA,cAAM,2CAAyB,KAAK,eAAeD,aAAY;AAAA,EACjE,CAAC;AACH;AAEA,eAAsB,0BACpB,KACA,QACA,oBACA,MAMA;AACA,QAAM,EAAE,cAAc,IAAI,UAAM,iCAAkB,GAAG;AASrD,QAAM,qBACJ,mBAAmB,eAAe,SAAS,KAAK,KAChD,CAAC,mBAAmB;AACtB,MAAI,oBAAoB;AACtB,QAAI,MAAM,SAAS;AACjB;AAAA,QACE;AAAA,QACA,kDAAkD,mBAAmB,IAAI;AAAA,MAC3E;AAAA,IACF;AACA;AAAA,EACF;AAEA,QAAM,aAAa,MAAM;AAAA,IACvB;AAAA,IACA,mBAAmB;AAAA,IACnB;AAAA,EACF;AAMA,QAAM,eAAe,CAAC;AAGtB,QAAM,iBAAiB,MAAM;AAAA,IAC3B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,eAAa,KAAK,GAAG,cAAc;AAGnC,QAAM,cAAc,MAAM;AAAA,IACxB;AAAA,IACA,mBAAmB;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,eAAa,KAAK,GAAG,WAAW;AAKhC,QAAM,WAAW,MAAM;AAAA,IACrB;AAAA,IACA,mBAAmB;AAAA,IACnB;AAAA,IACA;AAAA,IACA,MAAM,uBAAuB,cAAc;AAAA,IAC3C;AAAA,EACF;AACA,eAAa,KAAK,GAAG,QAAQ;AAG7B,aAAW,QAAQ,IAAI,GAAG,QAAQ,UAAU,GAAG;AAC7C,QAAI,CAAC,aAAa,SAAS,KAAK,IAAI,GAAG;AACrC,4CAAkB,KAAK,YAAAC,QAAK,KAAK,YAAY,KAAK,IAAI,GAAG,IAAI;AAAA,IAC/D;AAAA,EACF;AACF;AAEA,eAAsB,wBACpB,KACA,QACA,eACA,oBACA,mBACA,MAKA;AACA,QAAM,EAAE,cAAc,IAAI,UAAM,iCAAkB,GAAG;AAErD,QAAM,qBACJ,mBAAmB,eAAe,SAAS,KAAK,KAChD,CAAC,mBAAmB;AACtB,MAAI,oBAAoB;AACtB;AAAA,EACF;AAEA,QAAM,aAAa,YAAAA,QAAK,KAAK,mBAAmB,MAAM,YAAY;AAClE,MAAI,GAAG,MAAM,YAAY,EAAE,eAAe,MAAM,WAAW,KAAK,CAAC;AAIjE,QAAM,gBAAgB,iBAAiB,KAAK,mBAAmB,IAAI;AACnE,MAAI;AACJ,MAAI,eAAe;AACjB,QAAI,cAAc,QAAQ,iBAAiB;AACzC,0BAAoB,UAAM;AAAA,QACxB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,OAAO;AACL,8BAAoB,sCAAoB;AAAA,IAC1C;AAAA,EACF,OAAO;AACL,4BAAoB,uCAAqB;AAAA,EAC3C;AACA,QAAM,mBAAmB,YAAAA,QAAK,KAAK,YAAY,gBAAgB;AAC/D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,gBAAgB,YAAAA,QAAK,KAAK,YAAY,aAAa;AACzD,QAAM,iBAAiB,UAAM,4CAAmB,kBAAkB;AAClE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,aAAa,YAAAA,QAAK,KAAK,YAAY,UAAU;AACnD,QAAM,cAAc,UAAM;AAAA,IACxB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,EAAE,WAAW,cAAc,QAAQ,UAAU;AAAA,EAC/C;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,MAAI,MAAM,uBAAuB,cAAc,qBAAqB;AAClE,UAAM,gBAAgB,YAAAA,QAAK,KAAK,YAAY,cAAc;AAC1D,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAEA,eAAe,gBACb,KACA,QACAD,eACA,cACA,MACA;AACA,QAAM,aAAa,YAAAC,QAAK,KAAKD,eAAc,WAAW;AACtD,MAAI,gBAAgB,IAAI,GAAG,OAAO,UAAU,GAAG;AAC7C,mCAAW,KAAK,4BAA4B;AAC5C;AAAA,EACF;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,QACA,6BAAc;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,eAAe,kBACb,KACA,QACAA,eACA,cACA,MACA;AACA,QAAM,eAAe,YAAAC,QAAK,KAAKD,eAAc,eAAe;AAC5D,MAAI,gBAAgB,IAAI,GAAG,OAAO,YAAY,GAAG;AAC/C,mCAAW,KAAK,gCAAgC;AAChD;AAAA,EACF;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,QACA,iCAAgB;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,iBAAiB,KAAcA,eAAsB;AAC5D,MAAI,aAAa,YAAAC,QAAK,KAAKD,eAAc,WAAW;AACpD,MAAI,gBAAgB,IAAI,GAAG,OAAO,UAAU;AAC5C,MAAI,CAAC,eAAe;AAClB,iBAAa,YAAAC,QAAK,KAAKD,eAAc,WAAW;AAChD,oBAAgB,IAAI,GAAG,OAAO,UAAU;AAAA,EAC1C;AACA,SAAO;AACT;AAEA,eAAe,mBACb,KACA,QACAA,eACA,YACA,MACA;AACA,QAAM,gBAAgB,iBAAiB,KAAKA,aAAY;AACxD,QAAM,gBAAgB,oBAClB,sCAAoB,QACpB,uCAAqB;AAEzB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAAC,QAAK,KAAK,YAAY,gBAAgB;AAAA,IACtC;AAAA,EACF;AACA,SAAO,CAAC,gBAAgB;AAC1B;AAEA,eAAe,gBACb,KACA,QACA,YACA,MACA;AACA,QAAM,oBAAgB,6BAAc;AACpC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd;AAAA,IACA,YAAAA,QAAK,KAAK,YAAY,WAAW;AAAA,IACjC;AAAA,EACF;AAEA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd;AAAA,IACA,YAAAA,QAAK,KAAK,YAAY,aAAa;AAAA,IACnC;AAAA,EACF;AAEA,SAAO,CAAC,aAAa,aAAa;AACpC;AAEA,eAAe,gCACb,KACA,QACA,QACA,YACA,MACA;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,QACA,2CAAkB;AAAA,IAClB;AAAA,IACA,YAAAA,QAAK,KAAK,YAAY,WAAW;AAAA,IACjC;AAAA,EACF;AAIA,QAAM,gBAAgB,YAAAA,QAAK,KAAK,YAAY,aAAa;AACzD,MAAI,CAAC,IAAI,GAAG,OAAO,aAAa,GAAG;AACjC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,UACA,gDAAuB,MAAM;AAAA,MAC7B;AAAA,MACA,YAAAA,QAAK,KAAK,YAAY,aAAa;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AAEA,SAAO,CAAC,aAAa,aAAa;AACpC;AAEA,eAAe,mCACb,KACA,QACA,oBACA,YACA,MACA;AACA,QAAM,gBAAgB,iBAAiB,KAAK,mBAAmB,IAAI;AACnE,QAAM,mBAAmB,oBACrB,sCAAoB,QACpB,uCAAqB;AACzB,QAAM,gBAAgB,YAAAA,QAAK,KAAK,YAAY,gBAAgB;AAI5D,MAAI,CAAC,IAAI,GAAG,OAAO,aAAa,GAAG;AACjC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,SAAO,CAAC,gBAAgB;AAC1B;AAEA,eAAe,6BACb,KACA,QACA,QACA,YACA,qBACA,MACA;AACA,QAAM,YAAQ,qCAAe;AAC7B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAAA,QAAK,KAAK,YAAY,QAAQ;AAAA,IAC9B;AAAA,EACF;AAGA,QAAM,aAAa,YAAAA,QAAK,KAAK,YAAY,UAAU;AACnD,QAAM,iBAAa,0CAAoB;AACvC,MAAI,CAAC,IAAI,GAAG,OAAO,UAAU,GAAG;AAC9B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,QAAM,eAAe,CAAC,UAAU,UAAU;AAE1C,MAAI,uBAAuB,QAAQ;AACjC,UAAM,eAAW,0CAAoB;AACrC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAAA,QAAK,KAAK,YAAY,aAAa;AAAA,MACnC;AAAA,IACF;AAEA,UAAM,cAAc,YAAAA,QAAK,KAAK,YAAY,eAAe;AACzD,QAAI,CAAC,IAAI,GAAG,OAAO,WAAW,GAAG;AAC/B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,iBAAa,KAAK,eAAe,eAAe;AAAA,EAClD;AAEA,SAAO;AACT;AAEA,eAAe,aACb,KACA,QACAD,eACA,YACA,qBACA,MACA;AACA,QAAM,iBAAiB,UAAM,4BAAY,KAAKA,aAAY;AAC1D,QAAM,cAAc,eAAe,IAAI,CAAC,MAAM,YAAAC,QAAK,SAASD,eAAc,CAAC,CAAC;AAE5E,QAAM,iBAAa,uBAAW,WAAW;AACzC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA,YAAAC,QAAK,KAAK,YAAY,QAAQ;AAAA,IAC9B;AAAA,EACF;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA,YAAAA,QAAK,KAAK,YAAY,UAAU;AAAA,IAChC;AAAA,EACF;AACA,QAAM,eAAe,CAAC,UAAU,UAAU;AAE1C,MAAI,qBAAqB;AACvB,UAAM,oBAAgB,8BAAc,WAAW;AAC/C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,cAAc;AAAA,MACd;AAAA,MACA,YAAAA,QAAK,KAAK,YAAY,aAAa;AAAA,MACnC;AAAA,IACF;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,cAAc;AAAA,MACd;AAAA,MACA,YAAAA,QAAK,KAAK,YAAY,eAAe;AAAA,MACrC;AAAA,IACF;AACA,iBAAa,KAAK,eAAe,eAAe;AAAA,EAClD;AAEA,SAAO;AACT;AAEA,eAAe,mBACb,KACA,QACA,UACA,UACA,aACA,SAIA;AAKA,QAAM,oBAAoB,MAAM,gBAAAC,QAAS,OAAO,UAAU;AAAA,IACxD,QAAQ;AAAA,IACR,kBAAkB;AAAA,EACpB,CAAC;AACD,MAAI,SAAS,OAAO;AAGlB,kCAAU,KAAK,KAAK,YAAAD,QAAK,QAAQ,WAAW,CAAC,EAAE;AAC/C,kCAAU,KAAK,iBAAiB;AAChC;AAAA,EACF;AACA,MAAI;AACF,UAAM,WAAW,IAAI,GAAG,aAAa,WAAW;AAChD,QAAI,aAAa,mBAAmB;AAClC;AAAA,IACF;AAAA,EACF,SAAS,KAAU;AACjB,QAAI,IAAI,SAAS,UAAU;AAEzB,YAAM;AAAA,IACR;AAAA,EACF;AACA,MAAI,SAAS,QAAQ;AACnB,kCAAU,KAAK,6BAA6B,WAAW,EAAE;AACzD;AAAA,EACF;AACA,QAAM,UAAU,OAAO,cAAc,iBAAiB;AACtD,MAAI,GAAG,YAAY,SAAS,WAAW;AACzC;", "names": ["functionsDir", "path", "prettier"]}