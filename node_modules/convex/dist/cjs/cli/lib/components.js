"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var components_exports = {};
__export(components_exports, {
  runCodegen: () => runCodegen,
  runComponentsPush: () => runComponentsPush,
  runPush: () => runPush
});
module.exports = __toCommonJS(components_exports);
var import_path = __toESM(require("path"), 1);
var import_context = require("../../bundler/context.js");
var import_config = require("./config.js");
var import_deploy2 = require("./deploy2.js");
var import_version = require("../version.js");
var import_push = require("./push.js");
var import_utils = require("./utils/utils.js");
var import_bundle = require("./components/definition/bundle.js");
var import_directoryStructure = require("./components/definition/directoryStructure.js");
var import_codegen = require("./codegen.js");
var import_typecheck = require("./typecheck.js");
var import_fs = require("../../bundler/fs.js");
var import_debugBundlePath = require("./debugBundlePath.js");
var import_chalk = __toESM(require("chalk"), 1);
var import_api = require("./api.js");
var import_tracing = require("./tracing.js");
var import_constants = require("./components/constants.js");
async function findComponentRootPath(ctx, functionsDir2) {
  let componentRootPath = import_path.default.resolve(
    import_path.default.join(functionsDir2, import_constants.DEFINITION_FILENAME_TS)
  );
  if (!ctx.fs.exists(componentRootPath)) {
    componentRootPath = import_path.default.resolve(
      import_path.default.join(functionsDir2, import_constants.DEFINITION_FILENAME_JS)
    );
  }
  return componentRootPath;
}
async function runCodegen(ctx, deploymentSelection, options) {
  await (0, import_utils.ensureHasConvexDependency)(ctx, "codegen");
  const { configPath, projectConfig } = await (0, import_config.readProjectConfig)(ctx);
  const functionsDirectoryPath = (0, import_utils.functionsDir)(configPath, projectConfig);
  const componentRootPath = await findComponentRootPath(
    ctx,
    functionsDirectoryPath
  );
  if (ctx.fs.exists(componentRootPath)) {
    const selectionWithinProject = await (0, import_api.deploymentSelectionWithinProjectFromOptions)(ctx, options);
    const credentials = await (0, import_api.loadSelectedDeploymentCredentials)(
      ctx,
      deploymentSelection,
      selectionWithinProject
    );
    await startComponentsPushAndCodegen(
      ctx,
      import_tracing.Span.noop(),
      projectConfig,
      configPath,
      {
        ...options,
        deploymentName: credentials.deploymentFields?.deploymentName ?? null,
        url: credentials.url,
        adminKey: credentials.adminKey,
        generateCommonJSApi: options.commonjs,
        verbose: options.dryRun,
        codegen: true,
        liveComponentSources: options.liveComponentSources,
        typecheckComponents: false,
        debugNodeApis: options.debugNodeApis
      }
    );
  } else {
    if (options.init) {
      await (0, import_codegen.doInitCodegen)(ctx, functionsDirectoryPath, false, {
        dryRun: options.dryRun,
        debug: options.debug
      });
    }
    if (options.typecheck !== "disable") {
      (0, import_context.logMessage)(ctx, import_chalk.default.gray("Running TypeScript typecheck\u2026"));
    }
    await (0, import_codegen.doCodegen)(ctx, functionsDirectoryPath, options.typecheck, {
      dryRun: options.dryRun,
      debug: options.debug,
      generateCommonJSApi: options.commonjs
    });
  }
}
async function runPush(ctx, options) {
  const { configPath, projectConfig } = await (0, import_config.readProjectConfig)(ctx);
  const convexDir = (0, import_utils.functionsDir)(configPath, projectConfig);
  const componentRootPath = await findComponentRootPath(ctx, convexDir);
  if (ctx.fs.exists(componentRootPath)) {
    await runComponentsPush(ctx, options, configPath, projectConfig);
  } else {
    await (0, import_push.runNonComponentsPush)(ctx, options, configPath, projectConfig);
  }
}
async function startComponentsPushAndCodegen(ctx, parentSpan, projectConfig, configPath, options) {
  const convexDir = await (0, import_config.getFunctionsDirectoryPath)(ctx);
  const absWorkingDir = import_path.default.resolve(".");
  const isComponent = (0, import_directoryStructure.isComponentDirectory)(ctx, convexDir, true);
  if (isComponent.kind === "err") {
    return await ctx.crash({
      exitCode: 1,
      errorType: "invalid filesystem data",
      printedMessage: `Invalid component root directory (${isComponent.why}): ${convexDir}`
    });
  }
  const rootComponent = isComponent.component;
  (0, import_context.changeSpinner)(ctx, "Finding component definitions...");
  const { components, dependencyGraph } = await parentSpan.enterAsync(
    "componentGraph",
    () => (0, import_bundle.componentGraph)(
      ctx,
      absWorkingDir,
      rootComponent,
      !!options.liveComponentSources,
      options.verbose
    )
  );
  if (options.codegen) {
    (0, import_context.changeSpinner)(ctx, "Generating server code...");
    await parentSpan.enterAsync(
      "doInitialComponentCodegen",
      () => (0, import_fs.withTmpDir)(async (tmpDir) => {
        await (0, import_codegen.doInitialComponentCodegen)(ctx, tmpDir, rootComponent, options);
        for (const directory of components.values()) {
          await (0, import_codegen.doInitialComponentCodegen)(ctx, tmpDir, directory, options);
        }
      })
    );
  }
  (0, import_context.changeSpinner)(ctx, "Bundling component definitions...");
  const {
    appDefinitionSpecWithoutImpls,
    componentDefinitionSpecsWithoutImpls
  } = await parentSpan.enterAsync(
    "bundleDefinitions",
    () => (0, import_bundle.bundleDefinitions)(
      ctx,
      absWorkingDir,
      dependencyGraph,
      rootComponent,
      // Note that this *includes* the root component.
      [...components.values()],
      !!options.liveComponentSources
    )
  );
  if (options.debugNodeApis) {
    await (0, import_config.debugIsolateEndpointBundles)(ctx, projectConfig, configPath);
    (0, import_context.logFinishedStep)(
      ctx,
      "All non-'use node' entry points successfully bundled. Skipping rest of push."
    );
    return null;
  }
  (0, import_context.changeSpinner)(ctx, "Bundling component schemas and implementations...");
  const { appImplementation, componentImplementations } = await parentSpan.enterAsync(
    "bundleImplementations",
    () => (0, import_bundle.bundleImplementations)(
      ctx,
      rootComponent,
      [...components.values()],
      projectConfig.node.externalPackages,
      options.liveComponentSources ? ["@convex-dev/component-source"] : [],
      options.verbose
    )
  );
  if (options.debugBundlePath) {
    const { config: localConfig } = await (0, import_config.configFromProjectConfig)(
      ctx,
      projectConfig,
      configPath,
      options.verbose
    );
    await (0, import_debugBundlePath.handleDebugBundlePath)(ctx, options.debugBundlePath, localConfig);
    (0, import_context.logMessage)(
      ctx,
      `Wrote bundle and metadata for modules in the root to ${options.debugBundlePath}. Skipping rest of push.`
    );
    return null;
  }
  const udfServerVersion = import_version.version;
  const appDefinition = {
    ...appDefinitionSpecWithoutImpls,
    ...appImplementation,
    udfServerVersion
  };
  const componentDefinitions = [];
  for (const componentDefinition of componentDefinitionSpecsWithoutImpls) {
    const impl = componentImplementations.filter(
      (impl2) => impl2.definitionPath === componentDefinition.definitionPath
    )[0];
    if (!impl) {
      return await ctx.crash({
        exitCode: 1,
        errorType: "fatal",
        printedMessage: `missing! couldn't find ${componentDefinition.definitionPath} in ${componentImplementations.map((impl2) => impl2.definitionPath).toString()}`
      });
    }
    componentDefinitions.push({
      ...componentDefinition,
      ...impl,
      udfServerVersion
    });
  }
  const startPushRequest = {
    adminKey: options.adminKey,
    dryRun: options.dryRun,
    functions: projectConfig.functions,
    appDefinition,
    componentDefinitions,
    nodeDependencies: appImplementation.externalNodeDependencies
  };
  if (options.writePushRequest) {
    const pushRequestPath = import_path.default.resolve(options.writePushRequest);
    ctx.fs.writeUtf8File(
      `${pushRequestPath}.json`,
      JSON.stringify(startPushRequest)
    );
    return null;
  }
  logStartPushSizes(parentSpan, startPushRequest);
  (0, import_context.changeSpinner)(ctx, "Uploading functions to Convex...");
  const startPushResponse = await parentSpan.enterAsync(
    "startPush",
    (span) => (0, import_deploy2.startPush)(ctx, span, startPushRequest, options)
  );
  if (options.verbose) {
    (0, import_context.logMessage)(ctx, "startPush: " + JSON.stringify(startPushResponse, null, 2));
  }
  if (options.codegen) {
    (0, import_context.changeSpinner)(ctx, "Generating TypeScript bindings...");
    await parentSpan.enterAsync(
      "doFinalComponentCodegen",
      () => (0, import_fs.withTmpDir)(async (tmpDir) => {
        await (0, import_codegen.doFinalComponentCodegen)(
          ctx,
          tmpDir,
          rootComponent,
          rootComponent,
          startPushResponse,
          options
        );
        for (const directory of components.values()) {
          await (0, import_codegen.doFinalComponentCodegen)(
            ctx,
            tmpDir,
            rootComponent,
            directory,
            startPushResponse,
            options
          );
        }
      })
    );
  }
  (0, import_context.changeSpinner)(ctx, "Running TypeScript...");
  await parentSpan.enterAsync("typeCheckFunctionsInMode", async () => {
    await (0, import_typecheck.typeCheckFunctionsInMode)(ctx, options.typecheck, rootComponent.path);
    if (options.typecheckComponents) {
      for (const directory of components.values()) {
        await (0, import_typecheck.typeCheckFunctionsInMode)(ctx, options.typecheck, directory.path);
      }
    }
  });
  return startPushResponse;
}
function logStartPushSizes(span, startPushRequest) {
  let v8Size = 0;
  let v8Count = 0;
  let nodeSize = 0;
  let nodeCount = 0;
  for (const componentDefinition of startPushRequest.componentDefinitions) {
    for (const module2 of componentDefinition.functions) {
      if (module2.environment === "isolate") {
        v8Size += module2.source.length + (module2.sourceMap ?? "").length;
        v8Count += 1;
      } else if (module2.environment === "node") {
        nodeSize += module2.source.length + (module2.sourceMap ?? "").length;
        nodeCount += 1;
      }
    }
  }
  span.setProperty("v8_size", v8Size.toString());
  span.setProperty("v8_count", v8Count.toString());
  span.setProperty("node_size", nodeSize.toString());
  span.setProperty("node_count", nodeCount.toString());
}
async function runComponentsPush(ctx, options, configPath, projectConfig) {
  const reporter = new import_tracing.Reporter();
  const pushSpan = import_tracing.Span.root(reporter, "runComponentsPush");
  pushSpan.setProperty("cli_version", import_version.version);
  await (0, import_utils.ensureHasConvexDependency)(ctx, "push");
  const startPushResponse = await pushSpan.enterAsync(
    "startComponentsPushAndCodegen",
    (span) => startComponentsPushAndCodegen(
      ctx,
      span,
      projectConfig,
      configPath,
      options
    )
  );
  if (!startPushResponse) {
    return;
  }
  await pushSpan.enterAsync(
    "waitForSchema",
    (span) => (0, import_deploy2.waitForSchema)(ctx, span, startPushResponse, options)
  );
  const finishPushResponse = await pushSpan.enterAsync(
    "finishPush",
    (span) => (0, import_deploy2.finishPush)(ctx, span, startPushResponse, options)
  );
  printDiff(ctx, finishPushResponse, options);
  pushSpan.end();
  if (!options.dryRun) {
    void (0, import_deploy2.reportPushCompleted)(ctx, options.adminKey, options.url, reporter);
  }
}
function printDiff(ctx, finishPushResponse, opts) {
  if (opts.verbose) {
    const diffString = JSON.stringify(finishPushResponse, null, 2);
    (0, import_context.logMessage)(ctx, diffString);
    return;
  }
  const { componentDiffs } = finishPushResponse;
  let rootDiff = componentDiffs[""];
  if (rootDiff && rootDiff.indexDiff) {
    if (rootDiff.indexDiff.removed_indexes.length > 0) {
      let msg = `${opts.dryRun ? "Would delete" : "Deleted"} table indexes:
`;
      for (let i = 0; i < rootDiff.indexDiff.removed_indexes.length; i++) {
        const index = rootDiff.indexDiff.removed_indexes[i];
        if (i > 0) {
          msg += "\n";
        }
        msg += `  [-] ${formatIndex(index)}`;
      }
      (0, import_context.logFinishedStep)(ctx, msg);
    }
    if (rootDiff.indexDiff.added_indexes.length > 0) {
      let msg = `${opts.dryRun ? "Would add" : "Added"} table indexes:
`;
      for (let i = 0; i < rootDiff.indexDiff.added_indexes.length; i++) {
        const index = rootDiff.indexDiff.added_indexes[i];
        if (i > 0) {
          msg += "\n";
        }
        msg += `  [+] ${formatIndex(index)}`;
      }
      (0, import_context.logFinishedStep)(ctx, msg);
    }
  }
  for (const [componentPath, componentDiff] of Object.entries(componentDiffs)) {
    if (componentPath === "") {
      continue;
    }
    if (componentDiff.diffType.type === "create") {
      (0, import_context.logFinishedStep)(ctx, `Installed component ${componentPath}.`);
    }
    if (componentDiff.diffType.type === "unmount") {
      (0, import_context.logFinishedStep)(ctx, `Unmounted component ${componentPath}.`);
    }
    if (componentDiff.diffType.type === "remount") {
      (0, import_context.logFinishedStep)(ctx, `Remounted component ${componentPath}.`);
    }
  }
}
function formatIndex(index) {
  return `${index.name}`;
}
//# sourceMappingURL=components.js.map
