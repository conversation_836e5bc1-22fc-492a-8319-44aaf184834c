{"version": 3, "sources": ["../../../../src/cli/lib/dev.ts"], "sourcesContent": ["import chalk from \"chalk\";\nimport {\n  logError,\n  logFinishedStep,\n  logMessage,\n  logWarning,\n  OneoffCtx,\n  showSpinner,\n  showSpinnerIfSlow,\n  stopSpinner,\n} from \"../../bundler/context.js\";\nimport { runPush } from \"./components.js\";\nimport { performance } from \"perf_hooks\";\nimport path from \"path\";\nimport { LogManager, LogMode, watchLogs } from \"./logs.js\";\nimport { PushOptions } from \"./push.js\";\nimport {\n  formatDuration,\n  getCurrentTimeString,\n  spawnAsync,\n  waitForever,\n  waitUntilCalled,\n} from \"./utils/utils.js\";\nimport { Crash, WatchContext, Watcher } from \"./watch.js\";\nimport { runFunctionAndLog, subscribe } from \"./run.js\";\nimport { Value } from \"../../values/index.js\";\n\nexport async function devAgainstDeployment(\n  ctx: OneoffCtx,\n  credentials: {\n    url: string;\n    adminKey: string;\n    deploymentName: string | null;\n  },\n  devOptions: {\n    verbose: boolean;\n    typecheck: \"enable\" | \"try\" | \"disable\";\n    typecheckComponents: boolean;\n    codegen: boolean;\n    once: boolean;\n    untilSuccess: boolean;\n    run?:\n      | { kind: \"function\"; name: string; component?: string }\n      | { kind: \"shell\"; command: string };\n    tailLogs: LogMode;\n    traceEvents: boolean;\n    debugBundlePath?: string;\n    debugNodeApis: boolean;\n    liveComponentSources: boolean;\n  },\n) {\n  const logManager = new LogManager(devOptions.tailLogs);\n\n  const promises = [];\n  if (devOptions.tailLogs !== \"disable\") {\n    promises.push(\n      watchLogs(ctx, credentials.url, credentials.adminKey, \"stderr\", {\n        logManager,\n        success: false,\n      }),\n    );\n  }\n\n  promises.push(\n    watchAndPush(\n      ctx,\n      {\n        ...credentials,\n        verbose: devOptions.verbose,\n        dryRun: false,\n        typecheck: devOptions.typecheck,\n        typecheckComponents: devOptions.typecheckComponents,\n        debug: false,\n        debugBundlePath: devOptions.debugBundlePath,\n        debugNodeApis: devOptions.debugNodeApis,\n        codegen: devOptions.codegen,\n        liveComponentSources: devOptions.liveComponentSources,\n        logManager, // Pass logManager to control logs during deploy\n      },\n      devOptions,\n    ),\n  );\n  await Promise.race(promises);\n  await ctx.flushAndExit(0);\n}\n\nexport async function watchAndPush(\n  outerCtx: OneoffCtx,\n  options: PushOptions,\n  cmdOptions: {\n    run?:\n      | { kind: \"function\"; name: string; component?: string }\n      | { kind: \"shell\"; command: string };\n    once: boolean;\n    untilSuccess: boolean;\n    traceEvents: boolean;\n  },\n) {\n  const watch: { watcher: Watcher | undefined } = { watcher: undefined };\n  let numFailures = 0;\n  let ran = false;\n  let pushed = false;\n  let tableNameTriggeringRetry;\n  let shouldRetryOnDeploymentEnvVarChange;\n\n  while (true) {\n    const start = performance.now();\n    tableNameTriggeringRetry = null;\n    shouldRetryOnDeploymentEnvVarChange = false;\n\n    const ctx = new WatchContext(\n      cmdOptions.traceEvents,\n      outerCtx.bigBrainAuth(),\n    );\n    options.logManager?.beginDeploy();\n    showSpinner(ctx, \"Preparing Convex functions...\");\n    try {\n      await runPush(ctx, options);\n      const end = performance.now();\n      // NOTE: If `runPush` throws, `endDeploy` will not be called.\n      // This allows you to see the output from the failed deploy without\n      // logs getting in the way.\n      options.logManager?.endDeploy();\n      numFailures = 0;\n      logFinishedStep(\n        ctx,\n        `${getCurrentTimeString()} Convex functions ready! (${formatDuration(\n          end - start,\n        )})`,\n      );\n      if (cmdOptions.run !== undefined && !ran) {\n        switch (cmdOptions.run.kind) {\n          case \"function\":\n            await runFunctionInDev(\n              ctx,\n              options,\n              cmdOptions.run.name,\n              cmdOptions.run.component,\n            );\n            break;\n          case \"shell\":\n            try {\n              await spawnAsync(ctx, cmdOptions.run.command, [], {\n                stdio: \"inherit\",\n                shell: true,\n              });\n            } catch (e) {\n              // `spawnAsync` throws an error like `{ status: 1, error: Error }`\n              // when the command fails.\n              const errorMessage =\n                e === null || e === undefined\n                  ? null\n                  : (e as any).error instanceof Error\n                    ? ((e as any).error.message ?? null)\n                    : null;\n              const printedMessage = `Failed to run command \\`${cmdOptions.run.command}\\`: ${errorMessage ?? \"Unknown error\"}`;\n              // Don't return this since it'll bypass the `catch` below.\n              await ctx.crash({\n                exitCode: 1,\n                errorType: \"fatal\",\n                printedMessage,\n              });\n            }\n            break;\n          default: {\n            const _exhaustiveCheck: never = cmdOptions.run;\n            // Don't return this since it'll bypass the `catch` below.\n            await ctx.crash({\n              exitCode: 1,\n              errorType: \"fatal\",\n              printedMessage: `Unexpected arguments for --run`,\n              errForSentry: `Unexpected arguments for --run: ${JSON.stringify(\n                cmdOptions.run,\n              )}`,\n            });\n          }\n        }\n        ran = true;\n      }\n      pushed = true;\n    } catch (e: any) {\n      // Crash the app on unexpected errors.\n      if (!(e instanceof Crash) || !e.errorType) {\n        // eslint-disable-next-line no-restricted-syntax\n        throw e;\n      }\n      if (e.errorType === \"fatal\") {\n        break;\n      }\n      // Retry after an exponential backoff if we hit a transient error.\n      if (e.errorType === \"transient\") {\n        const delay = nextBackoff(numFailures);\n        numFailures += 1;\n        logWarning(\n          ctx,\n          chalk.yellow(\n            `Failed due to network error, retrying in ${formatDuration(\n              delay,\n            )}...`,\n          ),\n        );\n        await new Promise((resolve) => setTimeout(resolve, delay));\n        continue;\n      }\n\n      // Fall through if we had a filesystem-based error.\n      // TODO(sarah): Replace this with `logError`.\n      // eslint-disable-next-line no-console\n      console.assert(\n        e.errorType === \"invalid filesystem data\" ||\n          e.errorType === \"invalid filesystem or env vars\" ||\n          e.errorType[\"invalid filesystem or db data\"] !== undefined,\n      );\n      if (e.errorType === \"invalid filesystem or env vars\") {\n        shouldRetryOnDeploymentEnvVarChange = true;\n      } else if (\n        e.errorType !== \"invalid filesystem data\" &&\n        e.errorType[\"invalid filesystem or db data\"] !== undefined\n      ) {\n        tableNameTriggeringRetry = e.errorType[\"invalid filesystem or db data\"];\n      }\n      if (cmdOptions.once) {\n        await outerCtx.flushAndExit(1, e.errorType);\n      }\n      // Make sure that we don't spin if this push failed\n      // in any edge cases that didn't call `logFailure`\n      // before throwing.\n      stopSpinner(ctx);\n    }\n    if (cmdOptions.once) {\n      return;\n    }\n    if (pushed && cmdOptions.untilSuccess) {\n      return;\n    }\n    const fileSystemWatch = getFileSystemWatch(ctx, watch, cmdOptions);\n    const tableWatch = getTableWatch(\n      ctx,\n      options,\n      tableNameTriggeringRetry?.tableName ?? null,\n      tableNameTriggeringRetry?.componentPath,\n    );\n    const envVarWatch = getDeplymentEnvVarWatch(\n      ctx,\n      options,\n      shouldRetryOnDeploymentEnvVarChange,\n    );\n    await Promise.race([\n      fileSystemWatch.watch(),\n      tableWatch.watch(),\n      envVarWatch.watch(),\n    ]);\n    fileSystemWatch.stop();\n    void tableWatch.stop();\n    void envVarWatch.stop();\n  }\n}\n\nasync function runFunctionInDev(\n  ctx: WatchContext,\n  credentials: {\n    url: string;\n    adminKey: string;\n  },\n  functionName: string,\n  componentPath: string | undefined,\n) {\n  await runFunctionAndLog(ctx, {\n    deploymentUrl: credentials.url,\n    adminKey: credentials.adminKey,\n    functionName,\n    argsString: \"{}\",\n    componentPath,\n    callbacks: {\n      onSuccess: () => {\n        logFinishedStep(ctx, `Finished running function \"${functionName}\"`);\n      },\n    },\n  });\n}\n\nfunction getTableWatch(\n  ctx: WatchContext,\n  credentials: {\n    url: string;\n    adminKey: string;\n  },\n  tableName: string | null,\n  componentPath: string | undefined,\n) {\n  return getFunctionWatch(ctx, {\n    deploymentUrl: credentials.url,\n    adminKey: credentials.adminKey,\n    parsedFunctionName: \"_system/cli/queryTable\",\n    getArgs: () => (tableName !== null ? { tableName } : null),\n    componentPath,\n  });\n}\n\nfunction getDeplymentEnvVarWatch(\n  ctx: WatchContext,\n  credentials: {\n    url: string;\n    adminKey: string;\n  },\n  shouldRetryOnDeploymentEnvVarChange: boolean,\n) {\n  return getFunctionWatch(ctx, {\n    deploymentUrl: credentials.url,\n    adminKey: credentials.adminKey,\n    parsedFunctionName: \"_system/cli/queryEnvironmentVariables\",\n    getArgs: () => (shouldRetryOnDeploymentEnvVarChange ? {} : null),\n    componentPath: undefined,\n  });\n}\n\nfunction getFunctionWatch(\n  ctx: WatchContext,\n  args: {\n    deploymentUrl: string;\n    adminKey: string;\n    parsedFunctionName: string;\n    getArgs: () => Record<string, Value> | null;\n    componentPath: string | undefined;\n  },\n) {\n  const [stopPromise, stop] = waitUntilCalled();\n  return {\n    watch: async () => {\n      const functionArgs = args.getArgs();\n      if (functionArgs === null) {\n        return waitForever();\n      }\n      let changes = 0;\n      return subscribe(ctx, {\n        deploymentUrl: args.deploymentUrl,\n        adminKey: args.adminKey,\n        parsedFunctionName: args.parsedFunctionName,\n        parsedFunctionArgs: functionArgs,\n        componentPath: args.componentPath,\n        until: stopPromise,\n        callbacks: {\n          onChange: () => {\n            changes++;\n            // First bump is just the initial results reporting\n            if (changes > 1) {\n              stop();\n            }\n          },\n        },\n      });\n    },\n    stop: () => {\n      stop();\n    },\n  };\n}\n\nfunction getFileSystemWatch(\n  ctx: WatchContext,\n  watch: { watcher: Watcher | undefined },\n  cmdOptions: { traceEvents: boolean },\n) {\n  let hasStopped = false;\n  return {\n    watch: async () => {\n      const observations = ctx.fs.finalize();\n      if (observations === \"invalidated\") {\n        logMessage(ctx, \"Filesystem changed during push, retrying...\");\n        return;\n      }\n      // Initialize the watcher if we haven't done it already. Chokidar expects to have a\n      // nonempty watch set at initialization, so we can't do it before running our first\n      // push.\n      if (!watch.watcher) {\n        watch.watcher = new Watcher(observations);\n        await showSpinnerIfSlow(\n          ctx,\n          \"Preparing to watch files...\",\n          500,\n          async () => {\n            await watch.watcher!.ready();\n          },\n        );\n        stopSpinner(ctx);\n      }\n      // Watch new directories if needed.\n      watch.watcher.update(observations);\n\n      // Process events until we find one that overlaps with our previous observations.\n      let anyChanges = false;\n      do {\n        await watch.watcher.waitForEvent();\n        if (hasStopped) {\n          return;\n        }\n        for (const event of watch.watcher.drainEvents()) {\n          if (cmdOptions.traceEvents) {\n            logMessage(\n              ctx,\n              \"Processing\",\n              event.name,\n              path.relative(\"\", event.absPath),\n            );\n          }\n          const result = observations.overlaps(event);\n          if (result.overlaps) {\n            const relPath = path.relative(\"\", event.absPath);\n            if (cmdOptions.traceEvents) {\n              logMessage(ctx, `${relPath} ${result.reason}, rebuilding...`);\n            }\n            anyChanges = true;\n            break;\n          }\n        }\n      } while (!anyChanges);\n\n      // Wait for the filesystem to quiesce before starting a new push. It's okay to\n      // drop filesystem events at this stage since we're already committed to doing\n      // a push and resubscribing based on that push's observations.\n      let deadline = performance.now() + quiescenceDelay;\n      while (true) {\n        const now = performance.now();\n        if (now >= deadline) {\n          break;\n        }\n        const remaining = deadline - now;\n        if (cmdOptions.traceEvents) {\n          logMessage(\n            ctx,\n            `Waiting for ${formatDuration(remaining)} to quiesce...`,\n          );\n        }\n        const remainingWait = new Promise<\"timeout\">((resolve) =>\n          setTimeout(() => resolve(\"timeout\"), deadline - now),\n        );\n        const result = await Promise.race([\n          remainingWait,\n          watch.watcher.waitForEvent().then<\"newEvents\">(() => \"newEvents\"),\n        ]);\n        if (result === \"newEvents\") {\n          for (const event of watch.watcher.drainEvents()) {\n            const result = observations.overlaps(event);\n            // Delay another `quiescenceDelay` since we had an overlapping event.\n            if (result.overlaps) {\n              if (cmdOptions.traceEvents) {\n                logMessage(\n                  ctx,\n                  `Received an overlapping event at ${event.absPath}, delaying push.`,\n                );\n              }\n              deadline = performance.now() + quiescenceDelay;\n            }\n          }\n        } else {\n          // Let the check above `break` from the loop if we're past our deadlne.\n          if (result !== \"timeout\") {\n            logError(\n              ctx,\n              \"Assertion failed: Unexpected result from watcher: \" + result,\n            );\n          }\n        }\n      }\n    },\n    stop: () => {\n      hasStopped = true;\n    },\n  };\n}\n\nconst initialBackoff = 500;\nconst maxBackoff = 16000;\nconst quiescenceDelay = 500;\n\nexport function nextBackoff(prevFailures: number): number {\n  const baseBackoff = initialBackoff * Math.pow(2, prevFailures);\n  const actualBackoff = Math.min(baseBackoff, maxBackoff);\n  const jitter = actualBackoff * (Math.random() - 0.5);\n  return actualBackoff + jitter;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAkB;AAClB,qBASO;AACP,wBAAwB;AACxB,wBAA4B;AAC5B,kBAAiB;AACjB,kBAA+C;AAE/C,mBAMO;AACP,mBAA6C;AAC7C,iBAA6C;AAG7C,eAAsB,qBACpB,KACA,aAKA,YAgBA;AACA,QAAM,aAAa,IAAI,uBAAW,WAAW,QAAQ;AAErD,QAAM,WAAW,CAAC;AAClB,MAAI,WAAW,aAAa,WAAW;AACrC,aAAS;AAAA,UACP,uBAAU,KAAK,YAAY,KAAK,YAAY,UAAU,UAAU;AAAA,QAC9D;AAAA,QACA,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,WAAS;AAAA,IACP;AAAA,MACE;AAAA,MACA;AAAA,QACE,GAAG;AAAA,QACH,SAAS,WAAW;AAAA,QACpB,QAAQ;AAAA,QACR,WAAW,WAAW;AAAA,QACtB,qBAAqB,WAAW;AAAA,QAChC,OAAO;AAAA,QACP,iBAAiB,WAAW;AAAA,QAC5B,eAAe,WAAW;AAAA,QAC1B,SAAS,WAAW;AAAA,QACpB,sBAAsB,WAAW;AAAA,QACjC;AAAA;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,QAAQ,KAAK,QAAQ;AAC3B,QAAM,IAAI,aAAa,CAAC;AAC1B;AAEA,eAAsB,aACpB,UACA,SACA,YAQA;AACA,QAAM,QAA0C,EAAE,SAAS,OAAU;AACrE,MAAI,cAAc;AAClB,MAAI,MAAM;AACV,MAAI,SAAS;AACb,MAAI;AACJ,MAAI;AAEJ,SAAO,MAAM;AACX,UAAM,QAAQ,8BAAY,IAAI;AAC9B,+BAA2B;AAC3B,0CAAsC;AAEtC,UAAM,MAAM,IAAI;AAAA,MACd,WAAW;AAAA,MACX,SAAS,aAAa;AAAA,IACxB;AACA,YAAQ,YAAY,YAAY;AAChC,oCAAY,KAAK,+BAA+B;AAChD,QAAI;AACF,gBAAM,2BAAQ,KAAK,OAAO;AAC1B,YAAM,MAAM,8BAAY,IAAI;AAI5B,cAAQ,YAAY,UAAU;AAC9B,oBAAc;AACd;AAAA,QACE;AAAA,QACA,OAAG,mCAAqB,CAAC,iCAA6B;AAAA,UACpD,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AACA,UAAI,WAAW,QAAQ,UAAa,CAAC,KAAK;AACxC,gBAAQ,WAAW,IAAI,MAAM;AAAA,UAC3B,KAAK;AACH,kBAAM;AAAA,cACJ;AAAA,cACA;AAAA,cACA,WAAW,IAAI;AAAA,cACf,WAAW,IAAI;AAAA,YACjB;AACA;AAAA,UACF,KAAK;AACH,gBAAI;AACF,wBAAM,yBAAW,KAAK,WAAW,IAAI,SAAS,CAAC,GAAG;AAAA,gBAChD,OAAO;AAAA,gBACP,OAAO;AAAA,cACT,CAAC;AAAA,YACH,SAAS,GAAG;AAGV,oBAAM,eACJ,MAAM,QAAQ,MAAM,SAChB,OACC,EAAU,iBAAiB,QACxB,EAAU,MAAM,WAAW,OAC7B;AACR,oBAAM,iBAAiB,2BAA2B,WAAW,IAAI,OAAO,OAAO,gBAAgB,eAAe;AAE9G,oBAAM,IAAI,MAAM;AAAA,gBACd,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX;AAAA,cACF,CAAC;AAAA,YACH;AACA;AAAA,UACF,SAAS;AACP,kBAAM,mBAA0B,WAAW;AAE3C,kBAAM,IAAI,MAAM;AAAA,cACd,UAAU;AAAA,cACV,WAAW;AAAA,cACX,gBAAgB;AAAA,cAChB,cAAc,mCAAmC,KAAK;AAAA,gBACpD,WAAW;AAAA,cACb,CAAC;AAAA,YACH,CAAC;AAAA,UACH;AAAA,QACF;AACA,cAAM;AAAA,MACR;AACA,eAAS;AAAA,IACX,SAAS,GAAQ;AAEf,UAAI,EAAE,aAAa,uBAAU,CAAC,EAAE,WAAW;AAEzC,cAAM;AAAA,MACR;AACA,UAAI,EAAE,cAAc,SAAS;AAC3B;AAAA,MACF;AAEA,UAAI,EAAE,cAAc,aAAa;AAC/B,cAAM,QAAQ,YAAY,WAAW;AACrC,uBAAe;AACf;AAAA,UACE;AAAA,UACA,aAAAA,QAAM;AAAA,YACJ,gDAA4C;AAAA,cAC1C;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AACA,cAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,KAAK,CAAC;AACzD;AAAA,MACF;AAKA,cAAQ;AAAA,QACN,EAAE,cAAc,6BACd,EAAE,cAAc,oCAChB,EAAE,UAAU,+BAA+B,MAAM;AAAA,MACrD;AACA,UAAI,EAAE,cAAc,kCAAkC;AACpD,8CAAsC;AAAA,MACxC,WACE,EAAE,cAAc,6BAChB,EAAE,UAAU,+BAA+B,MAAM,QACjD;AACA,mCAA2B,EAAE,UAAU,+BAA+B;AAAA,MACxE;AACA,UAAI,WAAW,MAAM;AACnB,cAAM,SAAS,aAAa,GAAG,EAAE,SAAS;AAAA,MAC5C;AAIA,sCAAY,GAAG;AAAA,IACjB;AACA,QAAI,WAAW,MAAM;AACnB;AAAA,IACF;AACA,QAAI,UAAU,WAAW,cAAc;AACrC;AAAA,IACF;AACA,UAAM,kBAAkB,mBAAmB,KAAK,OAAO,UAAU;AACjE,UAAM,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,MACA,0BAA0B,aAAa;AAAA,MACvC,0BAA0B;AAAA,IAC5B;AACA,UAAM,cAAc;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,UAAM,QAAQ,KAAK;AAAA,MACjB,gBAAgB,MAAM;AAAA,MACtB,WAAW,MAAM;AAAA,MACjB,YAAY,MAAM;AAAA,IACpB,CAAC;AACD,oBAAgB,KAAK;AACrB,SAAK,WAAW,KAAK;AACrB,SAAK,YAAY,KAAK;AAAA,EACxB;AACF;AAEA,eAAe,iBACb,KACA,aAIA,cACA,eACA;AACA,YAAM,8BAAkB,KAAK;AAAA,IAC3B,eAAe,YAAY;AAAA,IAC3B,UAAU,YAAY;AAAA,IACtB;AAAA,IACA,YAAY;AAAA,IACZ;AAAA,IACA,WAAW;AAAA,MACT,WAAW,MAAM;AACf,4CAAgB,KAAK,8BAA8B,YAAY,GAAG;AAAA,MACpE;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAEA,SAAS,cACP,KACA,aAIA,WACA,eACA;AACA,SAAO,iBAAiB,KAAK;AAAA,IAC3B,eAAe,YAAY;AAAA,IAC3B,UAAU,YAAY;AAAA,IACtB,oBAAoB;AAAA,IACpB,SAAS,MAAO,cAAc,OAAO,EAAE,UAAU,IAAI;AAAA,IACrD;AAAA,EACF,CAAC;AACH;AAEA,SAAS,wBACP,KACA,aAIA,qCACA;AACA,SAAO,iBAAiB,KAAK;AAAA,IAC3B,eAAe,YAAY;AAAA,IAC3B,UAAU,YAAY;AAAA,IACtB,oBAAoB;AAAA,IACpB,SAAS,MAAO,sCAAsC,CAAC,IAAI;AAAA,IAC3D,eAAe;AAAA,EACjB,CAAC;AACH;AAEA,SAAS,iBACP,KACA,MAOA;AACA,QAAM,CAAC,aAAa,IAAI,QAAI,8BAAgB;AAC5C,SAAO;AAAA,IACL,OAAO,YAAY;AACjB,YAAM,eAAe,KAAK,QAAQ;AAClC,UAAI,iBAAiB,MAAM;AACzB,mBAAO,0BAAY;AAAA,MACrB;AACA,UAAI,UAAU;AACd,iBAAO,sBAAU,KAAK;AAAA,QACpB,eAAe,KAAK;AAAA,QACpB,UAAU,KAAK;AAAA,QACf,oBAAoB,KAAK;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe,KAAK;AAAA,QACpB,OAAO;AAAA,QACP,WAAW;AAAA,UACT,UAAU,MAAM;AACd;AAEA,gBAAI,UAAU,GAAG;AACf,mBAAK;AAAA,YACP;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,MAAM,MAAM;AACV,WAAK;AAAA,IACP;AAAA,EACF;AACF;AAEA,SAAS,mBACP,KACA,OACA,YACA;AACA,MAAI,aAAa;AACjB,SAAO;AAAA,IACL,OAAO,YAAY;AACjB,YAAM,eAAe,IAAI,GAAG,SAAS;AACrC,UAAI,iBAAiB,eAAe;AAClC,uCAAW,KAAK,6CAA6C;AAC7D;AAAA,MACF;AAIA,UAAI,CAAC,MAAM,SAAS;AAClB,cAAM,UAAU,IAAI,qBAAQ,YAAY;AACxC,kBAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA,YAAY;AACV,kBAAM,MAAM,QAAS,MAAM;AAAA,UAC7B;AAAA,QACF;AACA,wCAAY,GAAG;AAAA,MACjB;AAEA,YAAM,QAAQ,OAAO,YAAY;AAGjC,UAAI,aAAa;AACjB,SAAG;AACD,cAAM,MAAM,QAAQ,aAAa;AACjC,YAAI,YAAY;AACd;AAAA,QACF;AACA,mBAAW,SAAS,MAAM,QAAQ,YAAY,GAAG;AAC/C,cAAI,WAAW,aAAa;AAC1B;AAAA,cACE;AAAA,cACA;AAAA,cACA,MAAM;AAAA,cACN,YAAAC,QAAK,SAAS,IAAI,MAAM,OAAO;AAAA,YACjC;AAAA,UACF;AACA,gBAAM,SAAS,aAAa,SAAS,KAAK;AAC1C,cAAI,OAAO,UAAU;AACnB,kBAAM,UAAU,YAAAA,QAAK,SAAS,IAAI,MAAM,OAAO;AAC/C,gBAAI,WAAW,aAAa;AAC1B,6CAAW,KAAK,GAAG,OAAO,IAAI,OAAO,MAAM,iBAAiB;AAAA,YAC9D;AACA,yBAAa;AACb;AAAA,UACF;AAAA,QACF;AAAA,MACF,SAAS,CAAC;AAKV,UAAI,WAAW,8BAAY,IAAI,IAAI;AACnC,aAAO,MAAM;AACX,cAAM,MAAM,8BAAY,IAAI;AAC5B,YAAI,OAAO,UAAU;AACnB;AAAA,QACF;AACA,cAAM,YAAY,WAAW;AAC7B,YAAI,WAAW,aAAa;AAC1B;AAAA,YACE;AAAA,YACA,mBAAe,6BAAe,SAAS,CAAC;AAAA,UAC1C;AAAA,QACF;AACA,cAAM,gBAAgB,IAAI;AAAA,UAAmB,CAAC,YAC5C,WAAW,MAAM,QAAQ,SAAS,GAAG,WAAW,GAAG;AAAA,QACrD;AACA,cAAM,SAAS,MAAM,QAAQ,KAAK;AAAA,UAChC;AAAA,UACA,MAAM,QAAQ,aAAa,EAAE,KAAkB,MAAM,WAAW;AAAA,QAClE,CAAC;AACD,YAAI,WAAW,aAAa;AAC1B,qBAAW,SAAS,MAAM,QAAQ,YAAY,GAAG;AAC/C,kBAAMC,UAAS,aAAa,SAAS,KAAK;AAE1C,gBAAIA,QAAO,UAAU;AACnB,kBAAI,WAAW,aAAa;AAC1B;AAAA,kBACE;AAAA,kBACA,oCAAoC,MAAM,OAAO;AAAA,gBACnD;AAAA,cACF;AACA,yBAAW,8BAAY,IAAI,IAAI;AAAA,YACjC;AAAA,UACF;AAAA,QACF,OAAO;AAEL,cAAI,WAAW,WAAW;AACxB;AAAA,cACE;AAAA,cACA,uDAAuD;AAAA,YACzD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,MAAM,MAAM;AACV,mBAAa;AAAA,IACf;AAAA,EACF;AACF;AAEA,MAAM,iBAAiB;AACvB,MAAM,aAAa;AACnB,MAAM,kBAAkB;AAEjB,SAAS,YAAY,cAA8B;AACxD,QAAM,cAAc,iBAAiB,KAAK,IAAI,GAAG,YAAY;AAC7D,QAAM,gBAAgB,KAAK,IAAI,aAAa,UAAU;AACtD,QAAM,SAAS,iBAAiB,KAAK,OAAO,IAAI;AAChD,SAAO,gBAAgB;AACzB;", "names": ["chalk", "path", "result"]}