{"version": 3, "sources": ["../../../src/react/client.ts"], "sourcesContent": ["import { BaseConvexClient } from \"../browser/index.js\";\nimport type { OptimisticUpdate, QueryToken } from \"../browser/index.js\";\nimport React, { useCallback, useContext, useMemo } from \"react\";\nimport { convexToJson, Value } from \"../values/index.js\";\nimport { QueryJournal } from \"../browser/sync/protocol.js\";\nimport {\n  AuthTokenFetcher,\n  BaseConvexClientOptions,\n  ConnectionState,\n} from \"../browser/sync/client.js\";\nimport type { UserIdentityAttributes } from \"../browser/sync/protocol.js\";\nimport { RequestForQueries, useQueries } from \"./use_queries.js\";\nimport { useSubscription } from \"./use_subscription.js\";\nimport { parseArgs } from \"../common/index.js\";\nimport {\n  ArgsAndOptions,\n  FunctionArgs,\n  FunctionReference,\n  FunctionReturnType,\n  OptionalRestArgs,\n  getFunctionName,\n  makeFunctionReference,\n} from \"../server/api.js\";\nimport { EmptyObject } from \"../server/registration.js\";\nimport {\n  instantiateDefaultLogger,\n  instantiate<PERSON>oop<PERSON>ogger,\n  <PERSON><PERSON>,\n} from \"../browser/logging.js\";\n\nif (typeof React === \"undefined\") {\n  throw new Error(\"Required dependency 'react' not found\");\n}\n\n// TODO Typedoc doesn't generate documentation for the comment below perhaps\n// because it's a callable interface.\n/**\n * An interface to execute a Convex mutation function on the server.\n *\n * @public\n */\nexport interface ReactMutation<Mutation extends FunctionReference<\"mutation\">> {\n  /**\n   * Execute the mutation on the server, returning a `Promise` of its return value.\n   *\n   * @param args - Arguments for the mutation to pass up to the server.\n   * @returns The return value of the server-side function call.\n   */\n  (...args: OptionalRestArgs<Mutation>): Promise<FunctionReturnType<Mutation>>;\n\n  /**\n   * Define an optimistic update to apply as part of this mutation.\n   *\n   * This is a temporary update to the local query results to facilitate a\n   * fast, interactive UI. It enables query results to update before a mutation\n   * executed on the server.\n   *\n   * When the mutation is invoked, the optimistic update will be applied.\n   *\n   * Optimistic updates can also be used to temporarily remove queries from the\n   * client and create loading experiences until a mutation completes and the\n   * new query results are synced.\n   *\n   * The update will be automatically rolled back when the mutation is fully\n   * completed and queries have been updated.\n   *\n   * @param optimisticUpdate - The optimistic update to apply.\n   * @returns A new `ReactMutation` with the update configured.\n   *\n   * @public\n   */\n  withOptimisticUpdate<T extends OptimisticUpdate<FunctionArgs<Mutation>>>(\n    optimisticUpdate: T &\n      (ReturnType<T> extends Promise<any>\n        ? \"Optimistic update handlers must be synchronous\"\n        : {}),\n  ): ReactMutation<Mutation>;\n}\n\n// Exported only for testing.\nexport function createMutation(\n  mutationReference: FunctionReference<\"mutation\">,\n  client: ConvexReactClient,\n  update?: OptimisticUpdate<any>,\n): ReactMutation<any> {\n  function mutation(args?: Record<string, Value>): Promise<unknown> {\n    assertNotAccidentalArgument(args);\n\n    return client.mutation(mutationReference, args, {\n      optimisticUpdate: update,\n    });\n  }\n  mutation.withOptimisticUpdate = function withOptimisticUpdate(\n    optimisticUpdate: OptimisticUpdate<any>,\n  ): ReactMutation<any> {\n    if (update !== undefined) {\n      throw new Error(\n        `Already specified optimistic update for mutation ${getFunctionName(\n          mutationReference,\n        )}`,\n      );\n    }\n    return createMutation(mutationReference, client, optimisticUpdate);\n  };\n  return mutation as ReactMutation<any>;\n}\n\n/**\n * An interface to execute a Convex action on the server.\n *\n * @public\n */\nexport interface ReactAction<Action extends FunctionReference<\"action\">> {\n  /**\n   * Execute the function on the server, returning a `Promise` of its return value.\n   *\n   * @param args - Arguments for the function to pass up to the server.\n   * @returns The return value of the server-side function call.\n   * @public\n   */\n  (...args: OptionalRestArgs<Action>): Promise<FunctionReturnType<Action>>;\n}\n\nfunction createAction(\n  actionReference: FunctionReference<\"action\">,\n  client: ConvexReactClient,\n): ReactAction<any> {\n  return function (args?: Record<string, Value>): Promise<unknown> {\n    return client.action(actionReference, args);\n  } as ReactAction<any>;\n}\n\n/**\n * A watch on the output of a Convex query function.\n *\n * @public\n */\nexport interface Watch<T> {\n  /**\n   * Initiate a watch on the output of a query.\n   *\n   * This will subscribe to this query and call\n   * the callback whenever the query result changes.\n   *\n   * **Important: If the client is already subscribed to this query with the\n   * same arguments this callback will not be invoked until the query result is\n   * updated.** To get the current, local result call\n   * {@link react.Watch.localQueryResult}.\n   *\n   * @param callback - Function that is called whenever the query result changes.\n   * @returns - A function that disposes of the subscription.\n   */\n  onUpdate(callback: () => void): () => void;\n\n  /**\n   * Get the current result of a query.\n   *\n   * This will only return a result if we're already subscribed to the query\n   * and have received a result from the server or the query value has been set\n   * optimistically.\n   *\n   * @returns The result of the query or `undefined` if it isn't known.\n   * @throws An error if the query encountered an error on the server.\n   */\n  localQueryResult(): T | undefined;\n\n  /**\n   * @internal\n   */\n  localQueryLogs(): string[] | undefined;\n\n  /**\n   * Get the current {@link browser.QueryJournal} for this query.\n   *\n   * If we have not yet received a result for this query, this will be `undefined`.\n   */\n  journal(): QueryJournal | undefined;\n}\n\n/**\n * Options for {@link ConvexReactClient.watchQuery}.\n *\n * @public\n */\nexport interface WatchQueryOptions {\n  /**\n   * An (optional) journal produced from a previous execution of this query\n   * function.\n   *\n   * If there is an existing subscription to a query function with the same\n   * name and arguments, this journal will have no effect.\n   */\n  journal?: QueryJournal;\n\n  /**\n   * @internal\n   */\n  componentPath?: string;\n}\n\n/**\n * Options for {@link ConvexReactClient.mutation}.\n *\n * @public\n */\nexport interface MutationOptions<Args extends Record<string, Value>> {\n  /**\n   * An optimistic update to apply along with this mutation.\n   *\n   * An optimistic update locally updates queries while a mutation is pending.\n   * Once the mutation completes, the update will be rolled back.\n   */\n  optimisticUpdate?: OptimisticUpdate<Args>;\n}\n\n/**\n * Options for {@link ConvexReactClient}.\n *\n * @public\n */\nexport interface ConvexReactClientOptions extends BaseConvexClientOptions {}\n\n/**\n * A Convex client for use within React.\n *\n * This loads reactive queries and executes mutations over a WebSocket.\n *\n * @public\n */\nexport class ConvexReactClient {\n  private address: string;\n  private cachedSync?: BaseConvexClient;\n  private listeners: Map<QueryToken, Set<() => void>>;\n  private options: ConvexReactClientOptions;\n  private closed = false;\n  private _logger: Logger;\n\n  private adminAuth?: string;\n  private fakeUserIdentity?: UserIdentityAttributes;\n\n  /**\n   * @param address - The url of your Convex deployment, often provided\n   * by an environment variable. E.g. `https://small-mouse-123.convex.cloud`.\n   * @param options - See {@link ConvexReactClientOptions} for a full description.\n   */\n  constructor(address: string, options?: ConvexReactClientOptions) {\n    // Validate address immediately since validation by the lazily-instantiated\n    // internal client does not occur synchronously.\n    if (address === undefined) {\n      throw new Error(\n        \"No address provided to ConvexReactClient.\\n\" +\n          \"If trying to deploy to production, make sure to follow all the instructions found at https://docs.convex.dev/production/hosting/\\n\" +\n          \"If running locally, make sure to run `convex dev` and ensure the .env.local file is populated.\",\n      );\n    }\n    if (typeof address !== \"string\") {\n      throw new Error(\n        `ConvexReactClient requires a URL like 'https://happy-otter-123.convex.cloud', received something of type ${typeof address} instead.`,\n      );\n    }\n    if (!address.includes(\"://\")) {\n      throw new Error(\"Provided address was not an absolute URL.\");\n    }\n    this.address = address;\n    this.listeners = new Map();\n    this._logger =\n      options?.logger === false\n        ? instantiateNoopLogger({ verbose: options?.verbose ?? false })\n        : options?.logger !== true && options?.logger\n          ? options.logger\n          : instantiateDefaultLogger({ verbose: options?.verbose ?? false });\n    this.options = { ...options, logger: this._logger };\n  }\n\n  /**\n   * Return the address for this client, useful for creating a new client.\n   *\n   * Not guaranteed to match the address with which this client was constructed:\n   * it may be canonicalized.\n   */\n  get url() {\n    return this.address;\n  }\n\n  /**\n   * Lazily instantiate the `BaseConvexClient` so we don't create the WebSocket\n   * when server-side rendering.\n   *\n   * @internal\n   */\n  get sync() {\n    if (this.closed) {\n      throw new Error(\"ConvexReactClient has already been closed.\");\n    }\n    if (this.cachedSync) {\n      return this.cachedSync;\n    }\n    this.cachedSync = new BaseConvexClient(\n      this.address,\n      (updatedQueries) => this.transition(updatedQueries),\n      this.options,\n    );\n    if (this.adminAuth) {\n      this.cachedSync.setAdminAuth(this.adminAuth, this.fakeUserIdentity);\n    }\n    return this.cachedSync;\n  }\n\n  /**\n   * Set the authentication token to be used for subsequent queries and mutations.\n   * `fetchToken` will be called automatically again if a token expires.\n   * `fetchToken` should return `null` if the token cannot be retrieved, for example\n   * when the user's rights were permanently revoked.\n   * @param fetchToken - an async function returning the JWT-encoded OpenID Connect Identity Token\n   * @param onChange - a callback that will be called when the authentication status changes\n   */\n  setAuth(\n    fetchToken: AuthTokenFetcher,\n    onChange?: (isAuthenticated: boolean) => void,\n  ) {\n    if (typeof fetchToken === \"string\") {\n      throw new Error(\n        \"Passing a string to ConvexReactClient.setAuth is no longer supported, \" +\n          \"please upgrade to passing in an async function to handle reauthentication.\",\n      );\n    }\n    this.sync.setAuth(\n      fetchToken,\n      onChange ??\n        (() => {\n          // Do nothing\n        }),\n    );\n  }\n\n  /**\n   * Clear the current authentication token if set.\n   */\n  clearAuth() {\n    this.sync.clearAuth();\n  }\n\n  /**\n   * @internal\n   */\n  setAdminAuth(token: string, identity?: UserIdentityAttributes) {\n    this.adminAuth = token;\n    this.fakeUserIdentity = identity;\n    if (this.closed) {\n      throw new Error(\"ConvexReactClient has already been closed.\");\n    }\n    if (this.cachedSync) {\n      this.sync.setAdminAuth(token, identity);\n    }\n  }\n\n  /**\n   * Construct a new {@link Watch} on a Convex query function.\n   *\n   * **Most application code should not call this method directly. Instead use\n   * the {@link useQuery} hook.**\n   *\n   * @param query - A {@link server.FunctionReference} for the public query to run.\n   * @param args - An arguments object for the query. If this is omitted,\n   * the arguments will be `{}`.\n   * @param options - A {@link WatchQueryOptions} options object for this query.\n   *\n   * @returns The {@link Watch} object.\n   */\n  watchQuery<Query extends FunctionReference<\"query\">>(\n    query: Query,\n    ...argsAndOptions: ArgsAndOptions<Query, WatchQueryOptions>\n  ): Watch<FunctionReturnType<Query>> {\n    const [args, options] = argsAndOptions;\n    const name = getFunctionName(query);\n    return {\n      onUpdate: (callback) => {\n        const { queryToken, unsubscribe } = this.sync.subscribe(\n          name as string,\n          args,\n          options,\n        );\n\n        const currentListeners = this.listeners.get(queryToken);\n        if (currentListeners !== undefined) {\n          currentListeners.add(callback);\n        } else {\n          this.listeners.set(queryToken, new Set([callback]));\n        }\n\n        return () => {\n          if (this.closed) {\n            return;\n          }\n\n          const currentListeners = this.listeners.get(queryToken)!;\n          currentListeners.delete(callback);\n          if (currentListeners.size === 0) {\n            this.listeners.delete(queryToken);\n          }\n          unsubscribe();\n        };\n      },\n\n      localQueryResult: () => {\n        // Use the cached client because we can't have a query result if we don't\n        // even have a client yet!\n        if (this.cachedSync) {\n          return this.cachedSync.localQueryResult(name, args);\n        }\n        return undefined;\n      },\n\n      localQueryLogs: () => {\n        if (this.cachedSync) {\n          return this.cachedSync.localQueryLogs(name, args);\n        }\n        return undefined;\n      },\n\n      journal: () => {\n        if (this.cachedSync) {\n          return this.cachedSync.queryJournal(name, args);\n        }\n        return undefined;\n      },\n    };\n  }\n\n  /**\n   * Execute a mutation function.\n   *\n   * @param mutation - A {@link server.FunctionReference} for the public mutation\n   * to run.\n   * @param args - An arguments object for the mutation. If this is omitted,\n   * the arguments will be `{}`.\n   * @param options - A {@link MutationOptions} options object for the mutation.\n   * @returns A promise of the mutation's result.\n   */\n  mutation<Mutation extends FunctionReference<\"mutation\">>(\n    mutation: Mutation,\n    ...argsAndOptions: ArgsAndOptions<\n      Mutation,\n      MutationOptions<FunctionArgs<Mutation>>\n    >\n  ): Promise<FunctionReturnType<Mutation>> {\n    const [args, options] = argsAndOptions;\n    const name = getFunctionName(mutation);\n    return this.sync.mutation(name, args, options);\n  }\n\n  /**\n   * Execute an action function.\n   *\n   * @param action - A {@link server.FunctionReference} for the public action\n   * to run.\n   * @param args - An arguments object for the action. If this is omitted,\n   * the arguments will be `{}`.\n   * @returns A promise of the action's result.\n   */\n  action<Action extends FunctionReference<\"action\">>(\n    action: Action,\n    ...args: OptionalRestArgs<Action>\n  ): Promise<FunctionReturnType<Action>> {\n    const name = getFunctionName(action);\n    return this.sync.action(name, ...args);\n  }\n\n  /**\n   * Fetch a query result once.\n   *\n   * **Most application code should subscribe to queries instead, using\n   * the {@link useQuery} hook.**\n   *\n   * @param query - A {@link server.FunctionReference} for the public query\n   * to run.\n   * @param args - An arguments object for the query. If this is omitted,\n   * the arguments will be `{}`.\n   * @returns A promise of the query's result.\n   */\n  query<Query extends FunctionReference<\"query\">>(\n    query: Query,\n    ...args: OptionalRestArgs<Query>\n  ): Promise<FunctionReturnType<Query>> {\n    const watch = this.watchQuery(query, ...args);\n    const existingResult = watch.localQueryResult();\n    if (existingResult !== undefined) {\n      return Promise.resolve(existingResult);\n    }\n    return new Promise((resolve, reject) => {\n      const unsubscribe = watch.onUpdate(() => {\n        unsubscribe();\n        try {\n          resolve(watch.localQueryResult());\n        } catch (e) {\n          reject(e);\n        }\n      });\n    });\n  }\n\n  /**\n   * Get the current {@link ConnectionState} between the client and the Convex\n   * backend.\n   *\n   * @returns The {@link ConnectionState} with the Convex backend.\n   */\n  connectionState(): ConnectionState {\n    return this.sync.connectionState();\n  }\n\n  /**\n   * Subscribe to the {@link ConnectionState} between the client and the Convex\n   * backend, calling a callback each time it changes.\n   *\n   * Subscribed callbacks will be called when any part of ConnectionState changes.\n   * ConnectionState may grow in future versions (e.g. to provide a array of\n   * inflight requests) in which case callbacks would be called more frequently.\n   * ConnectionState may also *lose* properties in future versions as we figure\n   * out what information is most useful. As such this API is considered unstable.\n   *\n   * @returns An unsubscribe function to stop listening.\n   */\n  subscribeToConnectionState(\n    cb: (connectionState: ConnectionState) => void,\n  ): () => void {\n    return this.sync.subscribeToConnectionState(cb);\n  }\n\n  /**\n   * Get the logger for this client.\n   *\n   * @returns The {@link Logger} for this client.\n   */\n  get logger(): Logger {\n    return this._logger;\n  }\n\n  /**\n   * Close any network handles associated with this client and stop all subscriptions.\n   *\n   * Call this method when you're done with a {@link ConvexReactClient} to\n   * dispose of its sockets and resources.\n   *\n   * @returns A `Promise` fulfilled when the connection has been completely closed.\n   */\n  async close(): Promise<void> {\n    this.closed = true;\n    // Prevent outstanding React batched updates from invoking listeners.\n    this.listeners = new Map();\n    if (this.cachedSync) {\n      const sync = this.cachedSync;\n      this.cachedSync = undefined;\n      await sync.close();\n    }\n  }\n\n  private transition(updatedQueries: QueryToken[]) {\n    for (const queryToken of updatedQueries) {\n      const callbacks = this.listeners.get(queryToken);\n      if (callbacks) {\n        for (const callback of callbacks) {\n          callback();\n        }\n      }\n    }\n  }\n}\n\nconst ConvexContext = React.createContext<ConvexReactClient>(\n  undefined as unknown as ConvexReactClient, // in the future this will be a mocked client for testing\n);\n\n/**\n * Get the {@link ConvexReactClient} within a React component.\n *\n * This relies on the {@link ConvexProvider} being above in the React component tree.\n *\n * @returns The active {@link ConvexReactClient} object, or `undefined`.\n *\n * @public\n */\nexport function useConvex(): ConvexReactClient {\n  return useContext(ConvexContext);\n}\n\n/**\n * Provides an active Convex {@link ConvexReactClient} to descendants of this component.\n *\n * Wrap your app in this component to use Convex hooks `useQuery`,\n * `useMutation`, and `useConvex`.\n *\n * @param props - an object with a `client` property that refers to a {@link ConvexReactClient}.\n *\n * @public\n */\nexport const ConvexProvider: React.FC<{\n  client: ConvexReactClient;\n  children?: React.ReactNode;\n}> = ({ client, children }) => {\n  return React.createElement(\n    ConvexContext.Provider,\n    { value: client },\n    children,\n  );\n};\n\nexport type OptionalRestArgsOrSkip<FuncRef extends FunctionReference<any>> =\n  FuncRef[\"_args\"] extends EmptyObject\n    ? [args?: EmptyObject | \"skip\"]\n    : [args: FuncRef[\"_args\"] | \"skip\"];\n\n/**\n * Load a reactive query within a React component.\n *\n * This React hook contains internal state that will cause a rerender\n * whenever the query result changes.\n *\n * Throws an error if not used under {@link ConvexProvider}.\n *\n * @param query - a {@link server.FunctionReference} for the public query to run\n * like `api.dir1.dir2.filename.func`.\n * @param args - The arguments to the query function or the string \"skip\" if the\n * query should not be loaded.\n * @returns the result of the query. If the query is loading returns `undefined`.\n *\n * @public\n */\nexport function useQuery<Query extends FunctionReference<\"query\">>(\n  query: Query,\n  ...args: OptionalRestArgsOrSkip<Query>\n): Query[\"_returnType\"] | undefined {\n  const skip = args[0] === \"skip\";\n  const argsObject = args[0] === \"skip\" ? {} : parseArgs(args[0]);\n\n  const queryReference =\n    typeof query === \"string\"\n      ? makeFunctionReference<\"query\", any, any>(query)\n      : query;\n\n  const queryName = getFunctionName(queryReference);\n\n  const queries = useMemo(\n    () =>\n      skip\n        ? ({} as RequestForQueries)\n        : { query: { query: queryReference, args: argsObject } },\n    // Stringify args so args that are semantically the same don't trigger a\n    // rerender. Saves developers from adding `useMemo` on every args usage.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [JSON.stringify(convexToJson(argsObject)), queryName, skip],\n  );\n\n  const results = useQueries(queries);\n  const result = results[\"query\"];\n  if (result instanceof Error) {\n    throw result;\n  }\n  return result;\n}\n\n/**\n * Construct a new {@link ReactMutation}.\n *\n * Mutation objects can be called like functions to request execution of the\n * corresponding Convex function, or further configured with\n * [optimistic updates](https://docs.convex.dev/using/optimistic-updates).\n *\n * The value returned by this hook is stable across renders, so it can be used\n * by React dependency arrays and memoization logic relying on object identity\n * without causing rerenders.\n *\n * Throws an error if not used under {@link ConvexProvider}.\n *\n * @param mutation - A {@link server.FunctionReference} for the public mutation\n * to run like `api.dir1.dir2.filename.func`.\n * @returns The {@link ReactMutation} object with that name.\n *\n * @public\n */\nexport function useMutation<Mutation extends FunctionReference<\"mutation\">>(\n  mutation: Mutation,\n): ReactMutation<Mutation> {\n  const mutationReference =\n    typeof mutation === \"string\"\n      ? makeFunctionReference<\"mutation\", any, any>(mutation)\n      : mutation;\n\n  const convex = useContext(ConvexContext);\n  if (convex === undefined) {\n    throw new Error(\n      \"Could not find Convex client! `useMutation` must be used in the React component \" +\n        \"tree under `ConvexProvider`. Did you forget it? \" +\n        \"See https://docs.convex.dev/quick-start#set-up-convex-in-your-react-app\",\n    );\n  }\n  return useMemo(\n    () => createMutation(mutationReference, convex),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [convex, getFunctionName(mutationReference)],\n  );\n}\n\n/**\n * Construct a new {@link ReactAction}.\n *\n * Action objects can be called like functions to request execution of the\n * corresponding Convex function.\n *\n * The value returned by this hook is stable across renders, so it can be used\n * by React dependency arrays and memoization logic relying on object identity\n * without causing rerenders.\n *\n * Throws an error if not used under {@link ConvexProvider}.\n *\n * @param action - A {@link server.FunctionReference} for the public action\n * to run like `api.dir1.dir2.filename.func`.\n * @returns The {@link ReactAction} object with that name.\n *\n * @public\n */\nexport function useAction<Action extends FunctionReference<\"action\">>(\n  action: Action,\n): ReactAction<Action> {\n  const convex = useContext(ConvexContext);\n  const actionReference =\n    typeof action === \"string\"\n      ? makeFunctionReference<\"action\", any, any>(action)\n      : action;\n\n  if (convex === undefined) {\n    throw new Error(\n      \"Could not find Convex client! `useAction` must be used in the React component \" +\n        \"tree under `ConvexProvider`. Did you forget it? \" +\n        \"See https://docs.convex.dev/quick-start#set-up-convex-in-your-react-app\",\n    );\n  }\n  return useMemo(\n    () => createAction(actionReference, convex),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [convex, getFunctionName(actionReference)],\n  );\n}\n\n/**\n * React hook to get the current {@link ConnectionState} and subscribe to changes.\n *\n * This hook returns the current connection state and automatically rerenders\n * when any part of the connection state changes (e.g., when going online/offline,\n * when requests start/complete, etc.).\n *\n * The shape of ConnectionState may change in the future which may cause this\n * hook to rerender more frequently.\n *\n * Throws an error if not used under {@link ConvexProvider}.\n *\n * @returns The current {@link ConnectionState} with the Convex backend.\n *\n * @public\n */\nexport function useConvexConnectionState(): ConnectionState {\n  const convex = useContext(ConvexContext);\n  if (convex === undefined) {\n    throw new Error(\n      \"Could not find Convex client! `useConvexConnectionState` must be used in the React component \" +\n        \"tree under `ConvexProvider`. Did you forget it? \" +\n        \"See https://docs.convex.dev/quick-start#set-up-convex-in-your-react-app\",\n    );\n  }\n\n  const getCurrentValue = useCallback(() => {\n    return convex.connectionState();\n  }, [convex]);\n\n  const subscribe = useCallback(\n    (callback: () => void) => {\n      return convex.subscribeToConnectionState(() => {\n        callback();\n      });\n    },\n    [convex],\n  );\n\n  return useSubscription({ getCurrentValue, subscribe });\n}\n\n// When a function is called with a single argument that looks like a\n// React SyntheticEvent it was likely called as an event handler.\nfunction assertNotAccidentalArgument(value: any) {\n  // these are properties of a React.SyntheticEvent\n  // https://reactjs.org/docs/events.html\n  if (\n    typeof value === \"object\" &&\n    value !== null &&\n    \"bubbles\" in value &&\n    \"persist\" in value &&\n    \"isDefaultPrevented\" in value\n  ) {\n    throw new Error(\n      `Convex function called with SyntheticEvent object. Did you use a Convex function as an event handler directly? Event handlers like onClick receive an event object as their first argument. These SyntheticEvent objects are not valid Convex values. Try wrapping the function like \\`const handler = () => myMutation();\\` and using \\`handler\\` in the event handler.`,\n    );\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAAiC;AAEjC,mBAAwD;AACxD,oBAAoC;AAQpC,yBAA8C;AAC9C,8BAAgC;AAChC,oBAA0B;AAC1B,iBAQO;AAEP,qBAIO;AAEP,IAAI,OAAO,aAAAA,YAAU,aAAa;AAChC,QAAM,IAAI,MAAM,uCAAuC;AACzD;AAgDO,SAAS,eACd,mBACA,QACA,QACoB;AACpB,WAAS,SAAS,MAAgD;AAChE,gCAA4B,IAAI;AAEhC,WAAO,OAAO,SAAS,mBAAmB,MAAM;AAAA,MAC9C,kBAAkB;AAAA,IACpB,CAAC;AAAA,EACH;AACA,WAAS,uBAAuB,SAAS,qBACvC,kBACoB;AACpB,QAAI,WAAW,QAAW;AACxB,YAAM,IAAI;AAAA,QACR,wDAAoD;AAAA,UAClD;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO,eAAe,mBAAmB,QAAQ,gBAAgB;AAAA,EACnE;AACA,SAAO;AACT;AAkBA,SAAS,aACP,iBACA,QACkB;AAClB,SAAO,SAAU,MAAgD;AAC/D,WAAO,OAAO,OAAO,iBAAiB,IAAI;AAAA,EAC5C;AACF;AAmGO,MAAM,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgB7B,YAAY,SAAiB,SAAoC;AAfjE,wBAAQ;AACR,wBAAQ;AACR,wBAAQ;AACR,wBAAQ;AACR,wBAAQ,UAAS;AACjB,wBAAQ;AAER,wBAAQ;AACR,wBAAQ;AAUN,QAAI,YAAY,QAAW;AACzB,YAAM,IAAI;AAAA,QACR;AAAA,MAGF;AAAA,IACF;AACA,QAAI,OAAO,YAAY,UAAU;AAC/B,YAAM,IAAI;AAAA,QACR,4GAA4G,OAAO,OAAO;AAAA,MAC5H;AAAA,IACF;AACA,QAAI,CAAC,QAAQ,SAAS,KAAK,GAAG;AAC5B,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC7D;AACA,SAAK,UAAU;AACf,SAAK,YAAY,oBAAI,IAAI;AACzB,SAAK,UACH,SAAS,WAAW,YAChB,sCAAsB,EAAE,SAAS,SAAS,WAAW,MAAM,CAAC,IAC5D,SAAS,WAAW,QAAQ,SAAS,SACnC,QAAQ,aACR,yCAAyB,EAAE,SAAS,SAAS,WAAW,MAAM,CAAC;AACvE,SAAK,UAAU,EAAE,GAAG,SAAS,QAAQ,KAAK,QAAQ;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,MAAM;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,OAAO;AACT,QAAI,KAAK,QAAQ;AACf,YAAM,IAAI,MAAM,4CAA4C;AAAA,IAC9D;AACA,QAAI,KAAK,YAAY;AACnB,aAAO,KAAK;AAAA,IACd;AACA,SAAK,aAAa,IAAI;AAAA,MACpB,KAAK;AAAA,MACL,CAAC,mBAAmB,KAAK,WAAW,cAAc;AAAA,MAClD,KAAK;AAAA,IACP;AACA,QAAI,KAAK,WAAW;AAClB,WAAK,WAAW,aAAa,KAAK,WAAW,KAAK,gBAAgB;AAAA,IACpE;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,QACE,YACA,UACA;AACA,QAAI,OAAO,eAAe,UAAU;AAClC,YAAM,IAAI;AAAA,QACR;AAAA,MAEF;AAAA,IACF;AACA,SAAK,KAAK;AAAA,MACR;AAAA,MACA,aACG,MAAM;AAAA,MAEP;AAAA,IACJ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,SAAK,KAAK,UAAU;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,OAAe,UAAmC;AAC7D,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,QAAI,KAAK,QAAQ;AACf,YAAM,IAAI,MAAM,4CAA4C;AAAA,IAC9D;AACA,QAAI,KAAK,YAAY;AACnB,WAAK,KAAK,aAAa,OAAO,QAAQ;AAAA,IACxC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,WACE,UACG,gBAC+B;AAClC,UAAM,CAAC,MAAM,OAAO,IAAI;AACxB,UAAM,WAAO,4BAAgB,KAAK;AAClC,WAAO;AAAA,MACL,UAAU,CAAC,aAAa;AACtB,cAAM,EAAE,YAAY,YAAY,IAAI,KAAK,KAAK;AAAA,UAC5C;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAEA,cAAM,mBAAmB,KAAK,UAAU,IAAI,UAAU;AACtD,YAAI,qBAAqB,QAAW;AAClC,2BAAiB,IAAI,QAAQ;AAAA,QAC/B,OAAO;AACL,eAAK,UAAU,IAAI,YAAY,oBAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;AAAA,QACpD;AAEA,eAAO,MAAM;AACX,cAAI,KAAK,QAAQ;AACf;AAAA,UACF;AAEA,gBAAMC,oBAAmB,KAAK,UAAU,IAAI,UAAU;AACtD,UAAAA,kBAAiB,OAAO,QAAQ;AAChC,cAAIA,kBAAiB,SAAS,GAAG;AAC/B,iBAAK,UAAU,OAAO,UAAU;AAAA,UAClC;AACA,sBAAY;AAAA,QACd;AAAA,MACF;AAAA,MAEA,kBAAkB,MAAM;AAGtB,YAAI,KAAK,YAAY;AACnB,iBAAO,KAAK,WAAW,iBAAiB,MAAM,IAAI;AAAA,QACpD;AACA,eAAO;AAAA,MACT;AAAA,MAEA,gBAAgB,MAAM;AACpB,YAAI,KAAK,YAAY;AACnB,iBAAO,KAAK,WAAW,eAAe,MAAM,IAAI;AAAA,QAClD;AACA,eAAO;AAAA,MACT;AAAA,MAEA,SAAS,MAAM;AACb,YAAI,KAAK,YAAY;AACnB,iBAAO,KAAK,WAAW,aAAa,MAAM,IAAI;AAAA,QAChD;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,SACE,aACG,gBAIoC;AACvC,UAAM,CAAC,MAAM,OAAO,IAAI;AACxB,UAAM,WAAO,4BAAgB,QAAQ;AACrC,WAAO,KAAK,KAAK,SAAS,MAAM,MAAM,OAAO;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OACE,WACG,MACkC;AACrC,UAAM,WAAO,4BAAgB,MAAM;AACnC,WAAO,KAAK,KAAK,OAAO,MAAM,GAAG,IAAI;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,MACE,UACG,MACiC;AACpC,UAAM,QAAQ,KAAK,WAAW,OAAO,GAAG,IAAI;AAC5C,UAAM,iBAAiB,MAAM,iBAAiB;AAC9C,QAAI,mBAAmB,QAAW;AAChC,aAAO,QAAQ,QAAQ,cAAc;AAAA,IACvC;AACA,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,YAAM,cAAc,MAAM,SAAS,MAAM;AACvC,oBAAY;AACZ,YAAI;AACF,kBAAQ,MAAM,iBAAiB,CAAC;AAAA,QAClC,SAAS,GAAG;AACV,iBAAO,CAAC;AAAA,QACV;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,kBAAmC;AACjC,WAAO,KAAK,KAAK,gBAAgB;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,2BACE,IACY;AACZ,WAAO,KAAK,KAAK,2BAA2B,EAAE;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,SAAiB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,QAAuB;AAC3B,SAAK,SAAS;AAEd,SAAK,YAAY,oBAAI,IAAI;AACzB,QAAI,KAAK,YAAY;AACnB,YAAM,OAAO,KAAK;AAClB,WAAK,aAAa;AAClB,YAAM,KAAK,MAAM;AAAA,IACnB;AAAA,EACF;AAAA,EAEQ,WAAW,gBAA8B;AAC/C,eAAW,cAAc,gBAAgB;AACvC,YAAM,YAAY,KAAK,UAAU,IAAI,UAAU;AAC/C,UAAI,WAAW;AACb,mBAAW,YAAY,WAAW;AAChC,mBAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,MAAM,gBAAgB,aAAAD,QAAM;AAAA,EAC1B;AAAA;AACF;AAWO,SAAS,YAA+B;AAC7C,aAAO,yBAAW,aAAa;AACjC;AAYO,MAAM,iBAGR,CAAC,EAAE,QAAQ,SAAS,MAAM;AAC7B,SAAO,aAAAA,QAAM;AAAA,IACX,cAAc;AAAA,IACd,EAAE,OAAO,OAAO;AAAA,IAChB;AAAA,EACF;AACF;AAuBO,SAAS,SACd,UACG,MAC+B;AAClC,QAAM,OAAO,KAAK,CAAC,MAAM;AACzB,QAAM,aAAa,KAAK,CAAC,MAAM,SAAS,CAAC,QAAI,yBAAU,KAAK,CAAC,CAAC;AAE9D,QAAM,iBACJ,OAAO,UAAU,eACb,kCAAyC,KAAK,IAC9C;AAEN,QAAM,gBAAY,4BAAgB,cAAc;AAEhD,QAAM,cAAU;AAAA,IACd,MACE,OACK,CAAC,IACF,EAAE,OAAO,EAAE,OAAO,gBAAgB,MAAM,WAAW,EAAE;AAAA;AAAA;AAAA;AAAA,IAI3D,CAAC,KAAK,cAAU,4BAAa,UAAU,CAAC,GAAG,WAAW,IAAI;AAAA,EAC5D;AAEA,QAAM,cAAU,+BAAW,OAAO;AAClC,QAAM,SAAS,QAAQ,OAAO;AAC9B,MAAI,kBAAkB,OAAO;AAC3B,UAAM;AAAA,EACR;AACA,SAAO;AACT;AAqBO,SAAS,YACd,UACyB;AACzB,QAAM,oBACJ,OAAO,aAAa,eAChB,kCAA4C,QAAQ,IACpD;AAEN,QAAM,aAAS,yBAAW,aAAa;AACvC,MAAI,WAAW,QAAW;AACxB,UAAM,IAAI;AAAA,MACR;AAAA,IAGF;AAAA,EACF;AACA,aAAO;AAAA,IACL,MAAM,eAAe,mBAAmB,MAAM;AAAA;AAAA,IAE9C,CAAC,YAAQ,4BAAgB,iBAAiB,CAAC;AAAA,EAC7C;AACF;AAoBO,SAAS,UACd,QACqB;AACrB,QAAM,aAAS,yBAAW,aAAa;AACvC,QAAM,kBACJ,OAAO,WAAW,eACd,kCAA0C,MAAM,IAChD;AAEN,MAAI,WAAW,QAAW;AACxB,UAAM,IAAI;AAAA,MACR;AAAA,IAGF;AAAA,EACF;AACA,aAAO;AAAA,IACL,MAAM,aAAa,iBAAiB,MAAM;AAAA;AAAA,IAE1C,CAAC,YAAQ,4BAAgB,eAAe,CAAC;AAAA,EAC3C;AACF;AAkBO,SAAS,2BAA4C;AAC1D,QAAM,aAAS,yBAAW,aAAa;AACvC,MAAI,WAAW,QAAW;AACxB,UAAM,IAAI;AAAA,MACR;AAAA,IAGF;AAAA,EACF;AAEA,QAAM,sBAAkB,0BAAY,MAAM;AACxC,WAAO,OAAO,gBAAgB;AAAA,EAChC,GAAG,CAAC,MAAM,CAAC;AAEX,QAAM,gBAAY;AAAA,IAChB,CAAC,aAAyB;AACxB,aAAO,OAAO,2BAA2B,MAAM;AAC7C,iBAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,IACA,CAAC,MAAM;AAAA,EACT;AAEA,aAAO,yCAAgB,EAAE,iBAAiB,UAAU,CAAC;AACvD;AAIA,SAAS,4BAA4B,OAAY;AAG/C,MACE,OAAO,UAAU,YACjB,UAAU,QACV,aAAa,SACb,aAAa,SACb,wBAAwB,OACxB;AACA,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;", "names": ["React", "currentListeners"]}