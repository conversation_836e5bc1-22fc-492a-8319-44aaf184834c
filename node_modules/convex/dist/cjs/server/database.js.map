{"version": 3, "sources": ["../../../src/server/database.ts"], "sourcesContent": ["import { GenericId } from \"../values/index.js\";\nimport {\n  DocumentByName,\n  GenericDataModel,\n  NamedTableInfo,\n  TableNamesInDataModel,\n} from \"./data_model.js\";\nimport { QueryInitializer } from \"./query.js\";\nimport { SystemDataModel } from \"./schema.js\";\nimport {\n  WithOptionalSystemFields,\n  WithoutSystemFields,\n} from \"./system_fields.js\";\n\ninterface BaseDatabaseReader<DataModel extends GenericDataModel> {\n  /**\n   * Fetch a single document from the database by its {@link values.GenericId}.\n   *\n   * @param table - The name of the table to fetch the document from.\n   * @param id - The {@link values.GenericId} of the document to fetch from the database.\n   * @returns - The {@link GenericDocument} of the document at the given {@link values.GenericId}, or `null` if it no longer exists.\n   *\n   * @internal\n   */\n  get<TableName extends TableNamesInDataModel<DataModel>>(\n    table: NonUnion<TableName>,\n    id: GenericId<TableName>,\n  ): Promise<DocumentByName<DataModel, TableName> | null>;\n\n  /**\n   * Fetch a single document from the database by its {@link values.GenericId}.\n   *\n   * @param id - The {@link values.GenericId} of the document to fetch from the database.\n   * @returns - The {@link GenericDocument} of the document at the given {@link values.GenericId}, or `null` if it no longer exists.\n   */\n  get<TableName extends TableNamesInDataModel<DataModel>>(\n    id: GenericId<TableName>,\n  ): Promise<DocumentByName<DataModel, TableName> | null>;\n\n  /**\n   * Begin a query for the given table name.\n   *\n   * Queries don't execute immediately, so calling this method and extending its\n   * query are free until the results are actually used.\n   *\n   * @param tableName - The name of the table to query.\n   * @returns - A {@link QueryInitializer} object to start building a query.\n   */\n  query<TableName extends TableNamesInDataModel<DataModel>>(\n    tableName: TableName,\n  ): QueryInitializer<NamedTableInfo<DataModel, TableName>>;\n\n  /**\n   * Returns the string ID format for the ID in a given table, or null if the ID\n   * is from a different table or is not a valid ID.\n   *\n   * This accepts the string ID format as well as the `.toString()` representation\n   * of the legacy class-based ID format.\n   *\n   * This does not guarantee that the ID exists (i.e. `db.get(id)` may return `null`).\n   *\n   * @param tableName - The name of the table.\n   * @param id - The ID string.\n   */\n  normalizeId<TableName extends TableNamesInDataModel<DataModel>>(\n    tableName: TableName,\n    id: string,\n  ): GenericId<TableName> | null;\n}\n\ninterface BaseDatabaseReaderWithTable<DataModel extends GenericDataModel> {\n  /**\n   * Scope the database to a specific table.\n   */\n  table<TableName extends TableNamesInDataModel<DataModel>>(\n    tableName: TableName,\n  ): BaseTableReader<DataModel, TableName>;\n}\n\nexport interface BaseTableReader<\n  DataModel extends GenericDataModel,\n  TableName extends TableNamesInDataModel<DataModel>,\n> {\n  /**\n   * Fetch a single document from the table by its {@link values.GenericId}.\n   *\n   * @param id - The {@link values.GenericId} of the document to fetch from the database.\n   * @returns - The {@link GenericDocument} of the document at the given {@link values.GenericId}, or `null` if it no longer exists.\n   */\n  get(\n    id: GenericId<TableName>,\n  ): Promise<DocumentByName<DataModel, TableName> | null>;\n\n  /**\n   * Begin a query for the table.\n   *\n   * Queries don't execute immediately, so calling this method and extending its\n   * query are free until the results are actually used.\n   *\n   * @returns - A {@link QueryInitializer} object to start building a query.\n   */\n  query(): QueryInitializer<NamedTableInfo<DataModel, TableName>>;\n}\n\n/**\n * An interface to read from the database within Convex query functions.\n *\n * The two entry points are:\n *   - {@link GenericDatabaseReader.get}, which fetches a single document\n *     by its {@link values.GenericId}.\n *   - {@link GenericDatabaseReader.query}, which starts building a query.\n *\n * If you're using code generation, use the `DatabaseReader` type in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @public\n */\nexport interface GenericDatabaseReader<DataModel extends GenericDataModel>\n  extends BaseDatabaseReader<DataModel> {\n  /**\n   * An interface to read from the system tables within Convex query functions\n   *\n   * The two entry points are:\n   *   - {@link GenericDatabaseReader.get}, which fetches a single document\n   *     by its {@link values.GenericId}.\n   *   - {@link GenericDatabaseReader.query}, which starts building a query.\n   *\n   * @public\n   */\n  system: BaseDatabaseReader<SystemDataModel>;\n}\n\nexport interface GenericDatabaseReaderWithTable<\n  DataModel extends GenericDataModel,\n> extends BaseDatabaseReaderWithTable<DataModel> {\n  /**\n   * An interface to read from the system tables within Convex query functions\n   *\n   * The two entry points are:\n   *   - {@link GenericDatabaseReader.get}, which fetches a single document\n   *     by its {@link values.GenericId}.\n   *   - {@link GenericDatabaseReader.query}, which starts building a query.\n   *\n   * @public\n   */\n  system: BaseDatabaseReaderWithTable<SystemDataModel>;\n}\n\n/**\n * An interface to read from and write to the database within Convex mutation\n * functions.\n *\n * Convex guarantees that all writes within a single mutation are\n * executed atomically, so you never have to worry about partial writes leaving\n * your data in an inconsistent state. See [the Convex Guide](https://docs.convex.dev/understanding/convex-fundamentals/functions#atomicity-and-optimistic-concurrency-control)\n * for the guarantees Convex provides your functions.\n *\n *  If you're using code generation, use the `DatabaseReader` type in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @public\n */\nexport interface GenericDatabaseWriter<DataModel extends GenericDataModel>\n  extends GenericDatabaseReader<DataModel> {\n  /**\n   * Insert a new document into a table.\n   *\n   * @param table - The name of the table to insert a new document into.\n   * @param value - The {@link values.Value} to insert into the given table.\n   * @returns - {@link values.GenericId} of the new document.\n   */\n  insert<TableName extends TableNamesInDataModel<DataModel>>(\n    table: TableName,\n    value: WithoutSystemFields<DocumentByName<DataModel, TableName>>,\n  ): Promise<GenericId<TableName>>;\n\n  /**\n   * Patch an existing document, shallow merging it with the given partial\n   * document.\n   *\n   * New fields are added. Existing fields are overwritten. Fields set to\n   * `undefined` are removed.\n   *\n   * @param table - The name of the table the document is in.\n   * @param id - The {@link values.GenericId} of the document to patch.\n   * @param value - The partial {@link GenericDocument} to merge into the specified document. If this new value\n   * specifies system fields like `_id`, they must match the document's existing field values.\n   *\n   * @internal\n   */\n  patch<TableName extends TableNamesInDataModel<DataModel>>(\n    table: NonUnion<TableName>,\n    id: GenericId<TableName>,\n    value: Partial<DocumentByName<DataModel, TableName>>,\n  ): Promise<void>;\n\n  /**\n   * Patch an existing document, shallow merging it with the given partial\n   * document.\n   *\n   * New fields are added. Existing fields are overwritten. Fields set to\n   * `undefined` are removed.\n   *\n   * @param id - The {@link values.GenericId} of the document to patch.\n   * @param value - The partial {@link GenericDocument} to merge into the specified document. If this new value\n   * specifies system fields like `_id`, they must match the document's existing field values.\n   */\n  patch<TableName extends TableNamesInDataModel<DataModel>>(\n    id: GenericId<TableName>,\n    value: Partial<DocumentByName<DataModel, TableName>>,\n  ): Promise<void>;\n\n  /**\n   * Replace the value of an existing document, overwriting its old value.\n   *\n   * @param table - The name of the table the document is in.\n   * @param id - The {@link values.GenericId} of the document to replace.\n   * @param value - The new {@link GenericDocument} for the document. This value can omit the system fields,\n   * and the database will fill them in.\n   *\n   * @internal\n   */\n  replace<TableName extends TableNamesInDataModel<DataModel>>(\n    table: NonUnion<TableName>,\n    id: GenericId<TableName>,\n    value: WithOptionalSystemFields<DocumentByName<DataModel, TableName>>,\n  ): Promise<void>;\n\n  /**\n   * Replace the value of an existing document, overwriting its old value.\n   *\n   * @param id - The {@link values.GenericId} of the document to replace.\n   * @param value - The new {@link GenericDocument} for the document. This value can omit the system fields,\n   * and the database will fill them in.\n   */\n  replace<TableName extends TableNamesInDataModel<DataModel>>(\n    id: GenericId<TableName>,\n    value: WithOptionalSystemFields<DocumentByName<DataModel, TableName>>,\n  ): Promise<void>;\n\n  /**\n   * Delete an existing document.\n   *\n   * @param table - The name of the table the document is in.\n   * @param id - The {@link values.GenericId} of the document to remove.\n   *\n   * @internal\n   */\n  delete<TableName extends TableNamesInDataModel<DataModel>>(\n    table: NonUnion<TableName>,\n    id: GenericId<TableName>,\n  ): Promise<void>;\n\n  /**\n   * Delete an existing document.\n   *\n   * @param id - The {@link values.GenericId} of the document to remove.\n   */\n  delete(id: GenericId<TableNamesInDataModel<DataModel>>): Promise<void>;\n}\n\n/**\n * An interface to read from and write to the database within Convex mutation\n * functions.\n *\n * Convex guarantees that all writes within a single mutation are\n * executed atomically, so you never have to worry about partial writes leaving\n * your data in an inconsistent state. See [the Convex Guide](https://docs.convex.dev/understanding/convex-fundamentals/functions#atomicity-and-optimistic-concurrency-control)\n * for the guarantees Convex provides your functions.\n *\n *  If you're using code generation, use the `DatabaseReader` type in\n * `convex/_generated/server.d.ts` which is typed for your data model.\n *\n * @public\n */\nexport interface GenericDatabaseWriterWithTable<\n  DataModel extends GenericDataModel,\n> extends GenericDatabaseReaderWithTable<DataModel> {\n  /**\n   * Scope the database to a specific table.\n   */\n  table<TableName extends TableNamesInDataModel<DataModel>>(\n    tableName: TableName,\n  ): BaseTableWriter<DataModel, TableName>;\n}\n\nexport interface BaseTableWriter<\n  DataModel extends GenericDataModel,\n  TableName extends TableNamesInDataModel<DataModel>,\n> extends BaseTableReader<DataModel, TableName> {\n  /**\n   * Insert a new document into the table.\n   *\n   * @param value - The {@link values.Value} to insert into the given table.\n   * @returns - {@link values.GenericId} of the new document.\n   */\n  insert(\n    value: WithoutSystemFields<DocumentByName<DataModel, TableName>>,\n  ): Promise<GenericId<TableName>>;\n\n  /**\n   * Patch an existing document, shallow merging it with the given partial\n   * document.\n   *\n   * New fields are added. Existing fields are overwritten. Fields set to\n   * `undefined` are removed.\n   *\n   * @param id - The {@link values.GenericId} of the document to patch.\n   * @param value - The partial {@link GenericDocument} to merge into the specified document. If this new value\n   * specifies system fields like `_id`, they must match the document's existing field values.\n   */\n  patch(\n    id: GenericId<TableName>,\n    value: Partial<DocumentByName<DataModel, TableName>>,\n  ): Promise<void>;\n\n  /**\n   * Replace the value of an existing document, overwriting its old value.\n   *\n   * @param id - The {@link values.GenericId} of the document to replace.\n   * @param value - The new {@link GenericDocument} for the document. This value can omit the system fields,\n   * and the database will fill them in.\n   */\n  replace(\n    id: GenericId<TableName>,\n    value: WithOptionalSystemFields<DocumentByName<DataModel, TableName>>,\n  ): Promise<void>;\n\n  /**\n   * Delete an existing document.\n   *\n   * @param id - The {@link values.GenericId} of the document to remove.\n   */\n  delete(id: GenericId<TableName>): Promise<void>;\n}\n\n/**\n * This prevents TypeScript from inferring that the generic `TableName` type is\n * a union type when `table` and `id` disagree.\n */\ntype NonUnion<T> = T extends never // `never` is the bottom type for TypeScript unions\n  ? never\n  : T;\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}