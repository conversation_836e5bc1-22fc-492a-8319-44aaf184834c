{"version": 3, "sources": ["../../../../src/server/impl/database_impl.ts"], "sourcesContent": ["import {\n  convexTo<PERSON><PERSON>,\n  GenericId,\n  jsonToConvex,\n  Value,\n} from \"../../values/index.js\";\nimport { performAsyncSyscall, performSyscall } from \"./syscall.js\";\nimport {\n  GenericDatabaseReader,\n  GenericDatabaseReaderWithTable,\n  GenericDatabaseWriter,\n  GenericDatabaseWriterWithTable,\n} from \"../database.js\";\nimport { QueryInitializerImpl } from \"./query_impl.js\";\nimport { GenericDataModel, GenericDocument } from \"../data_model.js\";\nimport { validateArg } from \"./validate.js\";\nimport { version } from \"../../index.js\";\nimport { patchValueToJson } from \"../../values/value.js\";\n\nasync function get(\n  table: string | undefined,\n  id: GenericId<string>,\n  isSystem: boolean,\n) {\n  // If the user doesn’t provide any arguments, we use the new signature in the error message.\n  // We don’t do argument validation on the table argument since it’s not provided when using the old signature.\n  validateArg(id, 1, \"get\", \"id\");\n  if (typeof id !== \"string\") {\n    throw new Error(\n      `Invalid argument \\`id\\` for \\`db.get\\`, expected string but got '${typeof id}': ${\n        id as any\n      }`,\n    );\n  }\n  const args = {\n    id: convexToJson(id),\n    isSystem,\n    version,\n    table,\n  };\n  const syscallJSON = await performAsyncSyscall(\"1.0/get\", args);\n\n  return jsonToConvex(syscallJSON) as GenericDocument;\n}\n\nexport function setupReader(): GenericDatabaseReader<GenericDataModel> {\n  const reader = (\n    isSystem = false,\n  ): GenericDatabaseReader<GenericDataModel> &\n    GenericDatabaseReaderWithTable<GenericDataModel> => {\n    return {\n      get: async (arg0: any, arg1?: any) => {\n        return arg1 !== undefined\n          ? await get(arg0, arg1, isSystem)\n          : await get(undefined, arg0, isSystem);\n      },\n      query: (tableName: string) => {\n        return new TableReader(tableName, isSystem).query();\n      },\n      normalizeId: <TableName extends string>(\n        tableName: TableName,\n        id: string,\n      ): GenericId<TableName> | null => {\n        validateArg(tableName, 1, \"normalizeId\", \"tableName\");\n        validateArg(id, 2, \"normalizeId\", \"id\");\n        const accessingSystemTable = tableName.startsWith(\"_\");\n        if (accessingSystemTable !== isSystem) {\n          throw new Error(\n            `${\n              accessingSystemTable ? \"System\" : \"User\"\n            } tables can only be accessed from db.${\n              isSystem ? \"\" : \"system.\"\n            }normalizeId().`,\n          );\n        }\n        const syscallJSON = performSyscall(\"1.0/db/normalizeId\", {\n          table: tableName,\n          idString: id,\n        });\n        const syscallResult = jsonToConvex(syscallJSON) as any;\n        return syscallResult.id;\n      },\n      // We set the system reader on the next line\n      system: null as any,\n      table: (tableName) => {\n        return new TableReader(tableName, isSystem);\n      },\n    };\n  };\n  const { system: _, ...rest } = reader(true);\n  const r = reader();\n  r.system = rest as any;\n  return r;\n}\n\nasync function insert(tableName: string, value: any) {\n  if (tableName.startsWith(\"_\")) {\n    throw new Error(\"System tables (prefixed with `_`) are read-only.\");\n  }\n  validateArg(tableName, 1, \"insert\", \"table\");\n  validateArg(value, 2, \"insert\", \"value\");\n  const syscallJSON = await performAsyncSyscall(\"1.0/insert\", {\n    table: tableName,\n    value: convexToJson(value),\n  });\n  const syscallResult = jsonToConvex(syscallJSON) as any;\n  return syscallResult._id;\n}\n\nasync function patch(table: string | undefined, id: any, value: any) {\n  validateArg(id, 1, \"patch\", \"id\");\n  validateArg(value, 2, \"patch\", \"value\");\n  await performAsyncSyscall(\"1.0/shallowMerge\", {\n    id: convexToJson(id),\n    value: patchValueToJson(value as Value),\n    table,\n  });\n}\n\nasync function replace(table: string | undefined, id: any, value: any) {\n  validateArg(id, 1, \"replace\", \"id\");\n  validateArg(value, 2, \"replace\", \"value\");\n  await performAsyncSyscall(\"1.0/replace\", {\n    id: convexToJson(id),\n    value: convexToJson(value),\n    table,\n  });\n}\n\nasync function delete_(table: string | undefined, id: any) {\n  validateArg(id, 1, \"delete\", \"id\");\n  await performAsyncSyscall(\"1.0/remove\", {\n    id: convexToJson(id),\n    table,\n  });\n}\n\nexport function setupWriter(): GenericDatabaseWriter<GenericDataModel> &\n  GenericDatabaseWriterWithTable<GenericDataModel> {\n  const reader = setupReader();\n  return {\n    get: reader.get,\n    query: reader.query,\n    normalizeId: reader.normalizeId,\n    system: reader.system as any,\n    insert: async (table, value) => {\n      return await insert(table, value);\n    },\n    patch: async (arg0: any, arg1: any, arg2?: any) => {\n      return arg2 !== undefined\n        ? await patch(arg0, arg1, arg2)\n        : await patch(undefined, arg0, arg1);\n    },\n    replace: async (arg0: any, arg1: any, arg2?: any) => {\n      return arg2 !== undefined\n        ? await replace(arg0, arg1, arg2)\n        : await replace(undefined, arg0, arg1);\n    },\n    delete: async (arg0: any, arg1?: any) => {\n      return arg1 !== undefined\n        ? await delete_(arg0, arg1)\n        : await delete_(undefined, arg0);\n    },\n    table: (tableName) => {\n      return new TableWriter(tableName, false);\n    },\n  };\n}\n\nclass TableReader {\n  constructor(\n    protected readonly tableName: string,\n    protected readonly isSystem: boolean,\n  ) {}\n\n  async get(id: GenericId<string>) {\n    return get(this.tableName, id, this.isSystem);\n  }\n\n  query() {\n    const accessingSystemTable = this.tableName.startsWith(\"_\");\n    if (accessingSystemTable !== this.isSystem) {\n      throw new Error(\n        `${\n          accessingSystemTable ? \"System\" : \"User\"\n        } tables can only be accessed from db.${\n          this.isSystem ? \"\" : \"system.\"\n        }query().`,\n      );\n    }\n    return new QueryInitializerImpl(this.tableName);\n  }\n}\n\nclass TableWriter extends TableReader {\n  async insert(value: any) {\n    return insert(this.tableName, value);\n  }\n  async patch(id: any, value: any) {\n    return patch(this.tableName, id, value);\n  }\n  async replace(id: any, value: any) {\n    return replace(this.tableName, id, value);\n  }\n  async delete(id: any) {\n    return delete_(this.tableName, id);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAKO;AACP,qBAAoD;AAOpD,wBAAqC;AAErC,sBAA4B;AAC5B,eAAwB;AACxB,mBAAiC;AAEjC,eAAe,IACb,OACA,IACA,UACA;AAGA,mCAAY,IAAI,GAAG,OAAO,IAAI;AAC9B,MAAI,OAAO,OAAO,UAAU;AAC1B,UAAM,IAAI;AAAA,MACR,oEAAoE,OAAO,EAAE,MAC3E,EACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,OAAO;AAAA,IACX,QAAI,4BAAa,EAAE;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,cAAc,UAAM,oCAAoB,WAAW,IAAI;AAE7D,aAAO,4BAAa,WAAW;AACjC;AAEO,SAAS,cAAuD;AACrE,QAAM,SAAS,CACb,WAAW,UAEyC;AACpD,WAAO;AAAA,MACL,KAAK,OAAO,MAAW,SAAe;AACpC,eAAO,SAAS,SACZ,MAAM,IAAI,MAAM,MAAM,QAAQ,IAC9B,MAAM,IAAI,QAAW,MAAM,QAAQ;AAAA,MACzC;AAAA,MACA,OAAO,CAAC,cAAsB;AAC5B,eAAO,IAAI,YAAY,WAAW,QAAQ,EAAE,MAAM;AAAA,MACpD;AAAA,MACA,aAAa,CACX,WACA,OACgC;AAChC,yCAAY,WAAW,GAAG,eAAe,WAAW;AACpD,yCAAY,IAAI,GAAG,eAAe,IAAI;AACtC,cAAM,uBAAuB,UAAU,WAAW,GAAG;AACrD,YAAI,yBAAyB,UAAU;AACrC,gBAAM,IAAI;AAAA,YACR,GACE,uBAAuB,WAAW,MACpC,wCACE,WAAW,KAAK,SAClB;AAAA,UACF;AAAA,QACF;AACA,cAAM,kBAAc,+BAAe,sBAAsB;AAAA,UACvD,OAAO;AAAA,UACP,UAAU;AAAA,QACZ,CAAC;AACD,cAAM,oBAAgB,4BAAa,WAAW;AAC9C,eAAO,cAAc;AAAA,MACvB;AAAA;AAAA,MAEA,QAAQ;AAAA,MACR,OAAO,CAAC,cAAc;AACpB,eAAO,IAAI,YAAY,WAAW,QAAQ;AAAA,MAC5C;AAAA,IACF;AAAA,EACF;AACA,QAAM,EAAE,QAAQ,GAAG,GAAG,KAAK,IAAI,OAAO,IAAI;AAC1C,QAAM,IAAI,OAAO;AACjB,IAAE,SAAS;AACX,SAAO;AACT;AAEA,eAAe,OAAO,WAAmB,OAAY;AACnD,MAAI,UAAU,WAAW,GAAG,GAAG;AAC7B,UAAM,IAAI,MAAM,kDAAkD;AAAA,EACpE;AACA,mCAAY,WAAW,GAAG,UAAU,OAAO;AAC3C,mCAAY,OAAO,GAAG,UAAU,OAAO;AACvC,QAAM,cAAc,UAAM,oCAAoB,cAAc;AAAA,IAC1D,OAAO;AAAA,IACP,WAAO,4BAAa,KAAK;AAAA,EAC3B,CAAC;AACD,QAAM,oBAAgB,4BAAa,WAAW;AAC9C,SAAO,cAAc;AACvB;AAEA,eAAe,MAAM,OAA2B,IAAS,OAAY;AACnE,mCAAY,IAAI,GAAG,SAAS,IAAI;AAChC,mCAAY,OAAO,GAAG,SAAS,OAAO;AACtC,YAAM,oCAAoB,oBAAoB;AAAA,IAC5C,QAAI,4BAAa,EAAE;AAAA,IACnB,WAAO,+BAAiB,KAAc;AAAA,IACtC;AAAA,EACF,CAAC;AACH;AAEA,eAAe,QAAQ,OAA2B,IAAS,OAAY;AACrE,mCAAY,IAAI,GAAG,WAAW,IAAI;AAClC,mCAAY,OAAO,GAAG,WAAW,OAAO;AACxC,YAAM,oCAAoB,eAAe;AAAA,IACvC,QAAI,4BAAa,EAAE;AAAA,IACnB,WAAO,4BAAa,KAAK;AAAA,IACzB;AAAA,EACF,CAAC;AACH;AAEA,eAAe,QAAQ,OAA2B,IAAS;AACzD,mCAAY,IAAI,GAAG,UAAU,IAAI;AACjC,YAAM,oCAAoB,cAAc;AAAA,IACtC,QAAI,4BAAa,EAAE;AAAA,IACnB;AAAA,EACF,CAAC;AACH;AAEO,SAAS,cACmC;AACjD,QAAM,SAAS,YAAY;AAC3B,SAAO;AAAA,IACL,KAAK,OAAO;AAAA,IACZ,OAAO,OAAO;AAAA,IACd,aAAa,OAAO;AAAA,IACpB,QAAQ,OAAO;AAAA,IACf,QAAQ,OAAO,OAAO,UAAU;AAC9B,aAAO,MAAM,OAAO,OAAO,KAAK;AAAA,IAClC;AAAA,IACA,OAAO,OAAO,MAAW,MAAW,SAAe;AACjD,aAAO,SAAS,SACZ,MAAM,MAAM,MAAM,MAAM,IAAI,IAC5B,MAAM,MAAM,QAAW,MAAM,IAAI;AAAA,IACvC;AAAA,IACA,SAAS,OAAO,MAAW,MAAW,SAAe;AACnD,aAAO,SAAS,SACZ,MAAM,QAAQ,MAAM,MAAM,IAAI,IAC9B,MAAM,QAAQ,QAAW,MAAM,IAAI;AAAA,IACzC;AAAA,IACA,QAAQ,OAAO,MAAW,SAAe;AACvC,aAAO,SAAS,SACZ,MAAM,QAAQ,MAAM,IAAI,IACxB,MAAM,QAAQ,QAAW,IAAI;AAAA,IACnC;AAAA,IACA,OAAO,CAAC,cAAc;AACpB,aAAO,IAAI,YAAY,WAAW,KAAK;AAAA,IACzC;AAAA,EACF;AACF;AAEA,MAAM,YAAY;AAAA,EAChB,YACqB,WACA,UACnB;AAFmB;AACA;AAAA,EAClB;AAAA,EAEH,MAAM,IAAI,IAAuB;AAC/B,WAAO,IAAI,KAAK,WAAW,IAAI,KAAK,QAAQ;AAAA,EAC9C;AAAA,EAEA,QAAQ;AACN,UAAM,uBAAuB,KAAK,UAAU,WAAW,GAAG;AAC1D,QAAI,yBAAyB,KAAK,UAAU;AAC1C,YAAM,IAAI;AAAA,QACR,GACE,uBAAuB,WAAW,MACpC,wCACE,KAAK,WAAW,KAAK,SACvB;AAAA,MACF;AAAA,IACF;AACA,WAAO,IAAI,uCAAqB,KAAK,SAAS;AAAA,EAChD;AACF;AAEA,MAAM,oBAAoB,YAAY;AAAA,EACpC,MAAM,OAAO,OAAY;AACvB,WAAO,OAAO,KAAK,WAAW,KAAK;AAAA,EACrC;AAAA,EACA,MAAM,MAAM,IAAS,OAAY;AAC/B,WAAO,MAAM,KAAK,WAAW,IAAI,KAAK;AAAA,EACxC;AAAA,EACA,MAAM,QAAQ,IAAS,OAAY;AACjC,WAAO,QAAQ,KAAK,WAAW,IAAI,KAAK;AAAA,EAC1C;AAAA,EACA,MAAM,OAAO,IAAS;AACpB,WAAO,QAAQ,KAAK,WAAW,EAAE;AAAA,EACnC;AACF;", "names": []}