{"name": "center-align", "description": "Center-align the text in a string.", "version": "0.1.3", "homepage": "https://github.com/jonschlinkert/center-align", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/center-align", "bugs": {"url": "https://github.com/jonschlinkert/center-align/issues"}, "license": "MIT", "files": ["index.js", "utils.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"align-text": "^0.1.3", "lazy-cache": "^1.0.3"}, "devDependencies": {"mocha": "^2.2.0"}, "keywords": ["align", "align-center", "center", "center-align", "right", "right-align", "text", "typography"], "verb": {"related": {"description": "", "list": ["align-text", "right-align", "justified", "word-wrap"]}}}